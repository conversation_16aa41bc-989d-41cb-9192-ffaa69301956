#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化版週別資料處理程式
不依賴外部套件，專注於核心功能測試
"""

import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Tuple, Optional, Dict, Any

# 設定日誌
def setup_logging():
    """設定日誌配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('weekly_processor_simple.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

# 資料夾列表
AULINK_FOLDERS = [
    'DFLM_HKT-53001', 'DFLM_HKT-53002', 'DFLM_HKT-53003',
    'DV_V_AP-53201', 'DV_V_AP-53202', 'DV_V_AP-53301',
    'LA_NM-53001', 'LA_NM-53002', 'LA_NM-53003', 'LA_NM-53004',
    'MEC_FK-53101', 'MEC_FK-53301', 'SMVC_RH600-53002'
]

# 基礎路徑配置
BASE_LOG_PATH = r"\\k5dcnas02\IME\AAS_LOG"
BASE_OUTPUT_PATH = r"\\k5dcnas02\IME\個人資料夾\VICTOR\對對對對對\MO攔檢率\Rowdata"

# 週別計算函數
def get_week_number(date_str: str) -> int:
    """根據日期取得週別"""
    date = datetime.strptime(date_str, '%Y%m%d')
    base_date = datetime.strptime('20250202', '%Y%m%d')  # W6 開始日期
    days_diff = (date - base_date).days
    week_diff = days_diff // 7
    return week_diff + 6  # 從 W6 開始

def get_date_range_for_week(week: int) -> Tuple[datetime, datetime]:
    """取得指定週別的日期範圍"""
    base_date = datetime.strptime('20250202', '%Y%m%d')  # W6 開始日期
    days_to_add = (week - 6) * 7  # 從 W6 開始計算
    start_date = base_date + timedelta(days=days_to_add)
    end_date = start_date + timedelta(days=6)
    return start_date, end_date

def get_current_week() -> int:
    """取得當前週別"""
    today = datetime.now()
    today_str = today.strftime('%Y%m%d')
    return get_week_number(today_str)

# 檔案收集函數
def collect_week_files(folder: str, week: int) -> List[Path]:
    """收集指定週別的所有檔案"""
    start_date, end_date = get_date_range_for_week(week)
    
    # 獲取所有相關月份
    months_to_check = set()
    current_date = start_date
    while current_date <= end_date:
        months_to_check.add(current_date.strftime('%Y%m'))
        current_date += timedelta(days=1)
    
    logging.info(f"資料夾 {folder} 需要檢查的月份: {sorted(months_to_check)}")
    
    # 收集所有相關月份的檔案
    all_files = []
    base_folder = Path(BASE_LOG_PATH) / folder
    
    for month in months_to_check:
        month_folder = base_folder / month
        if month_folder.exists():
            month_files = list(month_folder.glob('*.log'))
            all_files.extend(month_files)
            logging.info(f"  月份 {month}: 找到 {len(month_files)} 個檔案")
        else:
            logging.warning(f"  月份 {month}: 資料夾不存在")
    
    # 過濾出該週的檔案
    week_files = []
    for f in all_files:
        try:
            file_date_str = f.stem.split('_')[-1]
            file_date = datetime.strptime(file_date_str, '%Y%m%d')
            if start_date <= file_date <= end_date:
                week_files.append(f)
        except (ValueError, IndexError) as e:
            logging.warning(f"無法解析檔案日期: {f.name}, 錯誤: {e}")
            continue
    
    logging.info(f"週別 W{week} 符合的檔案數量: {len(week_files)}")
    return week_files

# 文字替換功能
def process_single_file_text_replacement(file_path: Path, replacements: List[Tuple[str, str]]) -> Tuple[bool, Optional[Tuple[str, str]]]:
    """處理單個檔案的文字替換"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        new_content = content
        for old, new in replacements:
            new_content = new_content.replace(old, new)
        
        if new_content != content:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(new_content)
        
        return True, None
    except Exception as e:
        return False, (str(file_path), str(e))

def process_text_replacement_by_week(week: int, replacements: List[Tuple[str, str]], folders: Optional[List[str]] = None) -> Tuple[int, List[Tuple[str, str]]]:
    """週別文字替換處理"""
    if folders is None:
        folders = AULINK_FOLDERS
    
    start_date, end_date = get_date_range_for_week(week)
    logging.info(f"開始處理週別 W{week} 文字替換 ({start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')})")
    
    total_processed = 0
    total_failures = []
    
    for folder in folders:
        logging.info(f"處理資料夾: {folder}")
        
        week_files = collect_week_files(folder, week)
        if not week_files:
            logging.warning(f"週別 W{week} 沒有找到 {folder} 的檔案")
            continue
        
        folder_processed = 0
        folder_failures = []
        
        for file_path in week_files:
            success, result = process_single_file_text_replacement(file_path, replacements)
            if success:
                folder_processed += 1
            else:
                folder_failures.append(result)
        
        logging.info(f"{folder} 處理完成: 成功 {folder_processed} 個檔案")
        if folder_failures:
            logging.warning(f"{folder} 處理失敗: {len(folder_failures)} 個檔案")
        
        total_processed += folder_processed
        total_failures.extend(folder_failures)
    
    logging.info(f"週別 W{week} 文字替換完成: 總共處理 {total_processed} 個檔案")
    return total_processed, total_failures

# 簡化的 CSV 輸出功能（不使用 pandas）
def write_csv_output(data: List[Dict[str, Any]], output_file: Path):
    """寫入 CSV 檔案"""
    if not data:
        return
    
    # 取得所有欄位
    all_fields = set()
    for record in data:
        all_fields.update(record.keys())
    
    fields = sorted(all_fields)
    
    with open(output_file, 'w', encoding='utf-8-sig', newline='') as f:
        # 寫入標題
        f.write(','.join(fields) + '\n')
        
        # 寫入資料
        for record in data:
            row = []
            for field in fields:
                value = record.get(field, '')
                # 簡單的 CSV 轉義
                if isinstance(value, str) and (',' in value or '"' in value or '\n' in value):
                    value = '"' + value.replace('"', '""') + '"'
                row.append(str(value))
            f.write(','.join(row) + '\n')

# 預設替換規則
DEFAULT_REPLACEMENTS = [
    ('請按下""警報復歸""鈕', "請按下(警報復歸)鈕"),
    ('請按下警報復歸"鈕"', "請按下(警報復歸)鈕"), 
    ('"請按下""警報復歸""鈕"', "請按下(警報復歸)鈕"),
    ("請按下\"警報復歸\"鈕", "請按下(警報復歸)鈕"),
    ('"請按下(警報復歸)鈕"', "請按下(警報復歸)鈕")
]

def main():
    """主程式 - 處理上一週的資料"""
    setup_logging()
    
    # 取得當前週別，處理上一週
    current_week = get_current_week()
    process_week = current_week - 1
    
    start_date, end_date = get_date_range_for_week(process_week)
    logging.info(f"開始處理週別 W{process_week} ({start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')})")
    
    try:
        # 1. 先處理文字替換
        logging.info("="*60)
        logging.info("開始文字替換處理")
        logging.info("="*60)
        total_processed, failures = process_text_replacement_by_week(process_week, DEFAULT_REPLACEMENTS)
        
        if failures:
            logging.warning(f"文字替換有 {len(failures)} 個檔案處理失敗")
            for file_path, error in failures:
                logging.warning(f"失敗檔案: {file_path}, 錯誤: {error}")
        
        logging.info("="*60)
        logging.info(f"週別 W{process_week} 文字替換完成！")
        logging.info(f"文字替換處理了 {total_processed} 個檔案")
        logging.info("="*60)
        
    except Exception as e:
        logging.error(f"主程式執行錯誤: {str(e)}")

def test_functionality():
    """測試功能"""
    setup_logging()
    
    # 測試週別計算
    current_week = get_current_week()
    logging.info(f"當前週別: W{current_week}")
    
    test_week = current_week - 1
    start_date, end_date = get_date_range_for_week(test_week)
    logging.info(f"測試週別 W{test_week}: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
    
    # 測試檔案收集（只測試第一個資料夾）
    test_folder = AULINK_FOLDERS[0]
    logging.info(f"測試收集 {test_folder} 的檔案...")
    
    week_files = collect_week_files(test_folder, test_week)
    logging.info(f"找到 {len(week_files)} 個檔案")
    
    if week_files:
        logging.info("前5個檔案:")
        for i, file_path in enumerate(week_files[:5]):
            logging.info(f"  {i+1}. {file_path}")
    
    # 測試輸出路徑
    output_folder = Path(BASE_OUTPUT_PATH) / test_folder
    logging.info(f"輸出路徑: {output_folder}")
    logging.info(f"輸出路徑存在: {output_folder.exists()}")
    
    return len(week_files) > 0

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        print("執行測試模式...")
        test_functionality()
    else:
        print("執行主程式...")
        main()
