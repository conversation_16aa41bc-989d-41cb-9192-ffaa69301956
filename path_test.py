#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from pathlib import Path
from datetime import datetime

print("=== 路徑測試 ===")

# 測試網路路徑
BASE_LOG_PATH = r"\\k5dcnas02\IME\AAS_LOG"
BASE_OUTPUT_PATH = r"\\k5dcnas02\IME\個人資料夾\VICTOR\對對對對對\MO攔檢率\Rowdata"

print(f"Python 版本: {sys.version}")
print(f"當前時間: {datetime.now()}")

# 測試日誌路徑
try:
    log_path = Path(BASE_LOG_PATH)
    print(f"日誌路徑: {log_path}")
    print(f"日誌路徑存在: {log_path.exists()}")
    
    if log_path.exists():
        # 列出前幾個資料夾
        folders = [d for d in log_path.iterdir() if d.is_dir()]
        print(f"找到 {len(folders)} 個資料夾")
        for folder in sorted(folders)[:5]:
            print(f"  {folder.name}")
    
except Exception as e:
    print(f"日誌路徑錯誤: {e}")

print()

# 測試輸出路徑
try:
    output_path = Path(BASE_OUTPUT_PATH)
    print(f"輸出路徑: {output_path}")
    print(f"輸出路徑存在: {output_path.exists()}")
    
    if output_path.exists():
        # 列出前幾個資料夾
        folders = [d for d in output_path.iterdir() if d.is_dir()]
        print(f"找到 {len(folders)} 個資料夾")
        for folder in sorted(folders)[:5]:
            print(f"  {folder.name}")
    
except Exception as e:
    print(f"輸出路徑錯誤: {e}")

print()

# 測試特定資料夾
test_folder = "DFLM_HKT-53001"
try:
    test_path = Path(BASE_LOG_PATH) / test_folder
    print(f"測試資料夾: {test_path}")
    print(f"測試資料夾存在: {test_path.exists()}")
    
    if test_path.exists():
        # 列出月份資料夾
        months = [d for d in test_path.iterdir() if d.is_dir()]
        print(f"找到 {len(months)} 個月份資料夾")
        for month in sorted(months)[-3:]:  # 最近3個月
            print(f"  {month.name}")
            
            # 檢查檔案數量
            log_files = list(month.glob('*.log'))
            print(f"    {len(log_files)} 個 .log 檔案")
    
except Exception as e:
    print(f"測試資料夾錯誤: {e}")

print("=== 測試完成 ===")

# 嘗試寫入本地檔案
try:
    with open('path_test_result.txt', 'w', encoding='utf-8') as f:
        f.write(f"測試時間: {datetime.now()}\n")
        f.write(f"日誌路徑存在: {Path(BASE_LOG_PATH).exists()}\n")
        f.write(f"輸出路徑存在: {Path(BASE_OUTPUT_PATH).exists()}\n")
    print("結果已寫入 path_test_result.txt")
except Exception as e:
    print(f"寫入檔案失敗: {e}")
