# 週別處理程式說明

## 📋 程式概述

週別處理程式已拆分為獨立的模組，提供更靈活的使用方式：

### 🔧 程式檔案

1. **`text_replacement_processor.py`** - 文字替換處理程式
2. **`weekly_processor_fixed.py`** - Excel 資料處理程式  
3. **`weekly_complete_processor.py`** - 整合處理程式

## 🚀 使用方式

### 1. 完整處理（推薦）

```bash
# 處理上一週的完整資料（文字替換 + Excel 輸出）
python weekly_complete_processor.py

# 處理指定週別的完整資料
python weekly_complete_processor.py week=25
```

### 2. 只處理文字替換

```bash
# 處理上一週的文字替換
python text_replacement_processor.py

# 處理指定週別的文字替換
python text_replacement_processor.py week=25

# 或使用整合程式
python weekly_complete_processor.py text week=25
```

### 3. 只處理 Excel 輸出

```bash
# 處理上一週的 Excel 資料
python weekly_processor_fixed.py

# 處理指定週別的 Excel 資料
python weekly_processor_fixed.py week=25

# 或使用整合程式
python weekly_complete_processor.py excel week=25
```

### 4. 測試模式

```bash
# 測試文字替換功能
python text_replacement_processor.py test

# 測試 Excel 處理功能
python weekly_processor_fixed.py test
```

## 📁 處理流程

### 🔄 完整處理順序

1. **文字替換處理**
   - 修正警報復歸按鈕文字格式
   - 處理所有相關的 .log 檔案
   - 替換規則：
     - `請按下""警報復歸""鈕` → `請按下(警報復歸)鈕`
     - `請按下警報復歸"鈕"` → `請按下(警報復歸)鈕`
     - 等等...

2. **Excel 資料處理**
   - 解析處理後的 .log 檔案
   - 配對請求和回覆資料
   - 按服務名稱分工作表輸出

### 📊 輸出結果

#### Excel 檔案格式
- **檔案位置**: `\\k5dcnas02\IME\個人資料夾\VICTOR\對對對對對\MO攔檢率\Rowdata\{資料夾}\`
- **檔案名稱**: `{資料夾}_{年}_W{週別:02d}.xlsx`
- **工作表**: 按服務名稱分類（CheckTime, ChangeEQStatus, UploadData 等）

#### Excel 欄位說明
- `Rqst時間`: 請求時間
- `Rply時間`: 回覆時間
- `處理時間(秒)`: 請求到回覆的耗時
- `service_name`: 服務名稱
- `request_data_*`: 請求資料的各個欄位
- `reply_data_*`: 回覆資料的各個欄位

## 🗂️ 處理的資料夾

```
DFLM_HKT-53001, DFLM_HKT-53002, DFLM_HKT-53003
DV_V_AP-53201, DV_V_AP-53202, DV_V_AP-53301
LA_NM-53001, LA_NM-53002, LA_NM-53003, LA_NM-53004
MEC_FK-53101, MEC_FK-53301, SMVC_RH600-53002
```

## 📅 週別計算

- **基準日期**: 2025/02/02 為 W6 開始日期
- **當前週別**: 自動計算今日對應的週別
- **預設處理**: 當前週別 - 1（上一週）

## 📝 日誌檔案

每個程式都會生成對應的日誌檔案：

- `text_replacement_processor.log` - 文字替換日誌
- `excel_processor.log` - Excel 處理日誌
- `weekly_complete_processor.log` - 整合處理日誌

## ⚙️ 程式特色

### ✅ 優點

1. **模組化設計**: 可獨立執行各功能
2. **自動週別計算**: 無需手動計算日期範圍
3. **跨月份處理**: 自動處理跨月的週別資料
4. **詳細日誌**: 完整的處理過程記錄
5. **錯誤處理**: 完善的異常處理機制
6. **進度顯示**: 使用 tqdm 顯示處理進度

### 🔧 技術特點

1. **正確的日誌解析**: 基於實際日誌格式設計
2. **請求回覆配對**: 智能配對 [Rqst] 和 [Rply] 資料
3. **多線程處理**: 提升大量檔案的處理效率
4. **資料完整性**: 保留所有原始資料欄位

## 🚨 注意事項

1. **執行順序**: 建議先執行文字替換，再執行 Excel 處理
2. **網路路徑**: 確保可以存取 `\\k5dcnas02\IME\` 路徑
3. **檔案權限**: 確保有讀寫 .log 檔案的權限
4. **套件需求**: 需要安裝 `pandas`, `tqdm`, `openpyxl`

## 🛠️ 安裝套件

```bash
pip install pandas tqdm openpyxl
```

## 📞 使用說明

如需查看詳細使用說明：

```bash
python weekly_complete_processor.py help
```

## 🔍 故障排除

1. **沒有找到檔案**: 檢查網路路徑是否可存取
2. **Excel 沒有資料**: 確保先執行文字替換處理
3. **權限錯誤**: 檢查檔案讀寫權限
4. **日期解析錯誤**: 檢查檔案名稱格式是否正確

---

**建議使用方式**: 使用 `weekly_complete_processor.py` 進行完整處理，這樣可以確保正確的執行順序和最佳的處理效果。
