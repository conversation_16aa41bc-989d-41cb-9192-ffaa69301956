@echo off
chcp 65001 > nul
echo ========================================
echo 週別處理程式 EXE 打包工具
echo ========================================
echo.

echo 檢查 Python 環境...
python --version
if errorlevel 1 (
    echo ❌ Python 未安裝或不在 PATH 中
    pause
    exit /b 1
)

echo.
echo 檢查 byweek2.py 檔案...
if not exist "byweek2.py" (
    echo ❌ 找不到 byweek2.py 檔案
    echo 請確保 byweek2.py 在當前目錄中
    pause
    exit /b 1
)
echo ✅ 找到 byweek2.py 檔案

echo.
echo ========================================
echo 開始安裝必要套件...
echo ========================================

echo 安裝 pandas...
python -m pip install pandas
echo.

echo 安裝 tqdm...
python -m pip install tqdm
echo.

echo 安裝 openpyxl...
python -m pip install openpyxl
echo.

echo 安裝 pyinstaller...
python -m pip install pyinstaller
echo.

echo ========================================
echo 開始建立 EXE 檔案...
echo ========================================

python -m PyInstaller --onefile --console --name=byweek2_processor byweek2.py

if errorlevel 1 (
    echo ❌ EXE 建立失敗
    pause
    exit /b 1
)

echo.
echo ========================================
echo 🎉 EXE 建立成功！
echo ========================================
echo.
echo 檔案位置: dist\byweek2_processor.exe
echo.

echo 創建執行批次檔...
if not exist "dist" mkdir dist
echo @echo off > dist\run_processor.bat
echo chcp 65001 ^> nul >> dist\run_processor.bat
echo echo 週別處理程式 >> dist\run_processor.bat
echo echo ================ >> dist\run_processor.bat
echo echo. >> dist\run_processor.bat
echo echo 開始執行週別處理... >> dist\run_processor.bat
echo echo. >> dist\run_processor.bat
echo byweek2_processor.exe >> dist\run_processor.bat
echo echo. >> dist\run_processor.bat
echo echo 處理完成！ >> dist\run_processor.bat
echo pause >> dist\run_processor.bat

echo ✅ 已創建執行批次檔: dist\run_processor.bat

echo.
echo 創建說明檔案...
echo 週別處理程式 EXE 版本 > dist\README.txt
echo. >> dist\README.txt
echo 使用方式： >> dist\README.txt
echo 1. 雙擊 run_processor.bat 執行（推薦） >> dist\README.txt
echo 2. 或直接雙擊 byweek2_processor.exe >> dist\README.txt
echo. >> dist\README.txt
echo 程式會自動處理上一週的資料 >> dist\README.txt
echo 包含文字替換和 Excel 資料輸出功能 >> dist\README.txt

echo ✅ 已創建說明檔案: dist\README.txt

echo.
echo ========================================
echo 📋 完成！包含以下檔案：
echo ========================================
echo   📁 dist\
echo   ├── 📄 byweek2_processor.exe  (主程式)
echo   ├── 📄 run_processor.bat      (執行批次檔)
echo   └── 📄 README.txt             (使用說明)
echo.
echo 💡 建議使用 run_processor.bat 來執行程式
echo.

pause
