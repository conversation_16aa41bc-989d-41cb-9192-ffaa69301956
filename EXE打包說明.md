# byweek2.py EXE 打包說明

## 📋 檔案清單

我已經為您創建了以下打包檔案：

1. **`build_exe.bat`** - 一鍵打包批次檔（推薦使用）
2. **`package_to_exe.py`** - 完整版 Python 打包腳本
3. **`simple_package.py`** - 簡化版 Python 打包腳本
4. **`requirements.txt`** - 依賴套件清單

## 🚀 使用方式

### 方法一：使用批次檔（最簡單）

1. 確保 `byweek2.py` 和 `build_exe.bat` 在同一目錄
2. 雙擊執行 `build_exe.bat`
3. 等待自動安裝套件和建立 EXE
4. 完成後在 `dist` 資料夾中找到 EXE 檔案

### 方法二：使用 Python 腳本

```bash
# 使用完整版腳本
python package_to_exe.py

# 或使用簡化版腳本
python simple_package.py
```

### 方法三：手動執行

```bash
# 1. 安裝依賴套件
pip install -r requirements.txt

# 2. 使用 PyInstaller 建立 EXE
pyinstaller --onefile --console --name=byweek2_processor byweek2.py
```

## 📦 需要的套件

程式會自動檢測並安裝以下套件：

- **pandas** - 資料處理和 Excel 輸出
- **tqdm** - 進度條顯示
- **openpyxl** - Excel 檔案處理
- **pyinstaller** - EXE 打包工具

## 📁 輸出結果

打包完成後，會在 `dist` 資料夾中生成：

```
dist/
├── byweek2_processor.exe    # 主程式執行檔
├── run_processor.bat        # 執行批次檔
└── README.txt              # 使用說明
```

## 🎯 EXE 檔案功能

生成的 EXE 檔案包含完整的 byweek2.py 功能：

1. **自動週別計算** - 取得今日日期，處理上一週資料
2. **文字替換處理** - 修正警報復歸按鈕文字
3. **Excel 資料輸出** - 生成週別分析檔案
4. **多線程處理** - 提升處理效率
5. **詳細日誌記錄** - 完整的處理過程記錄

## 💡 使用建議

1. **推薦使用 `run_processor.bat`** 來執行 EXE，這樣可以看到完整的輸出訊息
2. **確保網路路徑可存取** - 程式需要存取 `\\k5dcnas02\IME\` 路徑
3. **檢查權限** - 確保有讀寫 .log 檔案的權限
4. **查看日誌** - 執行後檢查 `byweek2_processor.log` 了解處理結果

## 🔧 故障排除

### 打包失敗
- 確保 Python 已正確安裝並在 PATH 中
- 檢查網路連線，確保可以下載套件
- 嘗試手動安裝套件：`pip install pandas tqdm openpyxl pyinstaller`

### EXE 執行失敗
- 檢查是否有防毒軟體阻擋
- 確保網路路徑 `\\k5dcnas02\IME\` 可以存取
- 查看日誌檔案了解具體錯誤

### 套件相關問題
- 如果某個套件安裝失敗，可以嘗試：
  ```bash
  pip install --upgrade pip
  pip install 套件名稱 --force-reinstall
  ```

## 📋 檢查清單

打包前請確認：

- [ ] `byweek2.py` 檔案存在
- [ ] Python 環境正常
- [ ] 網路連線正常
- [ ] 有足夠的磁碟空間（建議至少 500MB）

打包後請確認：

- [ ] `dist` 資料夾已生成
- [ ] `byweek2_processor.exe` 檔案存在
- [ ] 執行 EXE 檔案無錯誤
- [ ] 可以正常存取網路路徑

## 🎉 完成

按照以上步驟，您就可以成功將 `byweek2.py` 打包成獨立的 EXE 檔案，方便在沒有 Python 環境的電腦上執行！

---

**建議執行順序：**
1. 雙擊 `build_exe.bat` 進行打包
2. 打包完成後，使用 `dist/run_processor.bat` 執行程式
3. 檢查日誌檔案確認處理結果
