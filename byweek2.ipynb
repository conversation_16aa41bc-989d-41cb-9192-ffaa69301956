{"cells": [{"cell_type": "code", "execution_count": 1, "id": "26c6488d-b692-460e-bf36-81fe8ee7e0bb", "metadata": {}, "outputs": [], "source": ["import os\n", "from pathlib import Path\n", "from concurrent.futures import ThreadPoolExecutor, as_completed\n", "from tqdm import tqdm\n", "from datetime import datetime, timedelta\n", "import logging\n", "from typing import List, Dict, Any\n", "import pandas as pd\n", "# from log_victor import *"]}, {"cell_type": "code", "execution_count": 2, "id": "c803e3b0-62cd-4b14-aa8d-f6ef470c70cc", "metadata": {}, "outputs": [], "source": ["aulink_folder = ['DFLM_HKT-53001','DFLM_HKT-53002', 'DFLM_HKT-53003',\n", " 'DV_V_AP-53201', 'DV_V_AP-53202', 'DV_V_AP-53301',\n", " 'LA_NM-53001', 'LA_NM-53002', 'LA_NM-53003',\n", " 'LA_NM-53004', 'MEC_FK-53101', 'MEC_FK-53301', 'SMVC_RH600-53002']  "]}, {"cell_type": "code", "execution_count": 12, "id": "7231496b-d72d-43f5-8bfd-1dcb147314ab", "metadata": {}, "outputs": [], "source": ["# aulink_folder = ['DV_V_AP-53202']"]}, {"cell_type": "code", "execution_count": 3, "id": "9dae8f2f-5f8d-4a2f-b527-6b737c9d771a", "metadata": {}, "outputs": [{"data": {"text/plain": ["'202505'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["current_date = datetime.now()\n", "\n", "# 獲取上個月的第一天\n", "first_day_of_current_month = current_date.replace(day=1)\n", "month_folder = first_day_of_current_month - <PERSON><PERSON><PERSON>(days=1)\n", "month_folder = month_folder.strftime('%Y%m')\n", "month_folder"]}, {"cell_type": "code", "execution_count": 4, "id": "4a33e668-fdc7-434f-a94b-4dddb56631b6", "metadata": {}, "outputs": [], "source": ["month_folder = '202506'"]}, {"cell_type": "markdown", "id": "5466b237-3927-4ba6-b32a-6204517baae5", "metadata": {}, "source": ["### 資料處理"]}, {"cell_type": "code", "execution_count": 5, "id": "1c29b391-7e1b-4953-acaf-fcab4fbd8538", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["處理檔案: 100%|████████████████████████████████████████████████████████████████████████| 20/20 [00:17<00:00,  1.17it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "DFLM_HKT-53001處理完成:\n", "成功處理 20 個檔案\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理檔案: 100%|████████████████████████████████████████████████████████████████████████| 20/20 [00:29<00:00,  1.48s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "DFLM_HKT-53002處理完成:\n", "成功處理 20 個檔案\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理檔案: 100%|████████████████████████████████████████████████████████████████████████| 20/20 [00:39<00:00,  1.98s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "DFLM_HKT-53003處理完成:\n", "成功處理 20 個檔案\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理檔案: 100%|████████████████████████████████████████████████████████████████████████| 20/20 [00:16<00:00,  1.21it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "DV_V_AP-53201處理完成:\n", "成功處理 20 個檔案\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理檔案: 100%|████████████████████████████████████████████████████████████████████████| 20/20 [00:17<00:00,  1.17it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "DV_V_AP-53202處理完成:\n", "成功處理 20 個檔案\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理檔案: 100%|████████████████████████████████████████████████████████████████████████| 20/20 [00:09<00:00,  2.20it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "DV_V_AP-53301處理完成:\n", "成功處理 20 個檔案\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理檔案: 100%|████████████████████████████████████████████████████████████████████████| 20/20 [00:14<00:00,  1.40it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "LA_NM-53001處理完成:\n", "成功處理 20 個檔案\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理檔案: 100%|████████████████████████████████████████████████████████████████████████| 20/20 [00:10<00:00,  1.89it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "LA_NM-53002處理完成:\n", "成功處理 20 個檔案\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理檔案: 100%|████████████████████████████████████████████████████████████████████████| 20/20 [00:13<00:00,  1.44it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "LA_NM-53003處理完成:\n", "成功處理 20 個檔案\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理檔案: 100%|████████████████████████████████████████████████████████████████████████| 20/20 [00:12<00:00,  1.61it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "LA_NM-53004處理完成:\n", "成功處理 20 個檔案\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理檔案: 100%|████████████████████████████████████████████████████████████████████████| 20/20 [00:33<00:00,  1.70s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "MEC_FK-53101處理完成:\n", "成功處理 20 個檔案\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理檔案: 100%|████████████████████████████████████████████████████████████████████████| 20/20 [00:06<00:00,  3.02it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "MEC_FK-53301處理完成:\n", "成功處理 20 個檔案\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理檔案: 100%|████████████████████████████████████████████████████████████████████████| 20/20 [00:11<00:00,  1.70it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "SMVC_RH600-53002處理完成:\n", "成功處理 20 個檔案\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["def process_single_file(file_path, replacements):\n", "    \"\"\"處理單個文件的函數\"\"\"\n", "    try:\n", "        # 讀取檔案內容\n", "        with open(file_path, 'r', encoding='utf-8') as file:\n", "            content = file.read()\n", "        \n", "        # 一次性進行所有替換\n", "        new_content = content\n", "        for old, new in replacements:\n", "            new_content = new_content.replace(old, new)\n", "            \n", "        # 只有在內容有變化時才寫入\n", "        if new_content != content:\n", "            with open(file_path, 'w', encoding='utf-8') as file:\n", "                file.write(new_content)\n", "                \n", "        return True, None\n", "        \n", "    except Exception as e:\n", "        return False, (str(file_path), str(e))\n", "\n", "def process_text_files(folder_path, replacements, max_workers=None):\n", "    \"\"\"使用多線程處理多個文件\"\"\"\n", "    if not os.path.exists(folder_path):\n", "        raise FileNotFoundError(f\"找不到資料夾: {folder_path}\")\n", "        \n", "    # 取得所有 log 檔案路徑\n", "    txt_files = list(Path(folder_path).glob(\"*.log\"))\n", "    \n", "    if not txt_files:\n", "        print(f\"在 {folder_path} 中沒有找到任何 log 檔案\")\n", "        return 0, []\n", "    \n", "    success_count = 0\n", "    failed_files = []\n", "    \n", "    # 使用進度條顯示處理進度\n", "    with ThreadPoolExecutor(max_workers=max_workers) as executor:\n", "        # 創建任務列表\n", "        future_to_file = {\n", "            executor.submit(process_single_file, file_path, replacements): file_path \n", "            for file_path in txt_files\n", "        }\n", "        \n", "        # 使用tqdm顯示進度\n", "        with tqdm(total=len(txt_files), desc=\"處理檔案\") as pbar:\n", "            for future in future_to_file:\n", "                success, result = future.result()\n", "                if success:\n", "                    success_count += 1\n", "                else:\n", "                    failed_files.append(result)\n", "                pbar.update(1)\n", "    \n", "    return success_count, failed_files\n", "\n", "# 使用範例\n", "if __name__ == \"__main__\":\n", "    # 設定替換規則\n", "    replacements = [\n", "        ('請按下\"\"警報復歸\"\"鈕', \"請按下(警報復歸)鈕\"),\n", "        ('請按下警報復歸\"鈕\"', \"請按下(警報復歸)鈕\"), \n", "        ('\"請按下\"\"警報復歸\"\"鈕\"', \"請按下(警報復歸)鈕\"),\n", "        (\"請按下\\\"警報復歸\\\"鈕\", \"請按下(警報復歸)鈕\"),\n", "        ('\"請按下(警報復歸)鈕\"', \"請按下(警報復歸)鈕\")\n", "    ]   #\"請按下(警報復歸)鈕\"\n", "    # 改為週別處理\n", "    # 設定要處理的週別\n", "    today = datetime.now()\n", "    today_str = today.strftime('%Y%m%d')\n", "    current_week = get_week_number(today_str)\n", "    process_week = current_week - 1  # 處理上一週\n", "    \n", "    # 取得週別的日期範圍\n", "    start_date, end_date = get_date_range_for_week(process_week)\n", "    print(f\"處理週別 W{process_week}: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}\")\n", "    \n", "    for folder in aulink_folder:\n", "        print(f\"\\n開始處理資料夾: {folder}\")\n", "        \n", "        # 獲取所有相關月份的檔案\n", "        all_files = []\n", "        months_to_check = set()\n", "        current_date = start_date\n", "        while current_date <= end_date:\n", "            months_to_check.add(current_date.strftime('%Y%m'))\n", "            current_date += <PERSON><PERSON><PERSON>(days=1)\n", "        \n", "        print(f\"需要檢查的月份: {sorted(months_to_check)}\")\n", "        \n", "        # 收集所有相關月份的檔案\n", "        base_folder = Path(fr\"\\\\k5dcnas02\\IME\\AAS_LOG\\{folder}\")\n", "        for month in months_to_check:\n", "            month_folder_path = base_folder / month\n", "            if month_folder_path.exists():\n", "                month_files = list(month_folder_path.glob('*.log'))\n", "                all_files.extend(month_files)\n", "                print(f\"  月份 {month}: 找到 {len(month_files)} 個檔案\")\n", "            else:\n", "                print(f\"  月份 {month}: 資料夾不存在\")\n", "        \n", "        if not all_files:\n", "            print(f\"  警告：找不到週別 W{process_week} 的相關檔案\")\n", "            continue\n", "        \n", "        # 過濾出該週的檔案\n", "        week_files = []\n", "        for f in all_files:\n", "            try:\n", "                # 從檔案名稱中提取日期\n", "                file_date_str = f.stem.split('_')[-1]\n", "                file_date = datetime.strptime(file_date_str, '%Y%m%d')\n", "                if start_date <= file_date <= end_date:\n", "                    week_files.append(f)\n", "            except (ValueError, IndexError) as e:\n", "                print(f\"  警告：無法解析檔案日期: {f.name}, 錯誤: {e}\")\n", "                continue\n", "        \n", "        print(f\"  週別 W{process_week} 符合的檔案數量: {len(week_files)}\")\n", "        \n", "        if not week_files:\n", "            print(f\"  警告：週別 W{process_week} 沒有符合的檔案\")\n", "            continue\n", "        \n", "        try:\n", "            # 執行週別檔案處理\n", "            total_processed = 0\n", "            total_failures = []\n", "            \n", "            for file_path in week_files:\n", "                success, result = process_single_file(file_path, replacements)\n", "                if success:\n", "                    total_processed += 1\n", "                else:\n", "                    total_failures.append(result)\n", "            \n", "            # 輸出結果\n", "            print(f\"\\n{folder} 週別 W{process_week} 處理完成:\")\n", "            print(f\"成功處理 {total_processed} 個檔案\")\n", "            \n", "            if total_failures:\n", "                print(\"\\n處理失敗的檔案:\")\n", "                for file_path, error in total_failures:\n", "                    print(f\"- {file_path}: {error}\")\n", "                    \n", "        except Exception as e:\n", "            print(f\"執行過程發生錯誤: {e}\")"]}, {"cell_type": "markdown", "id": "text-replacement-title", "metadata": {}, "source": ["### 週別文字替換處理\\n", "\\n", "以下是文字替換功能的週別處理版本：\\n", "\\n", "**主要改進：**\\n", "1. 自動根據週別範圍收集檔案\\n", "2. 跨月份檔案處理\\n", "3. 詳細的處理進度顯示\\n", "4. 更好的錯誤處理和日誌記錄"]}, {"cell_type": "code", "execution_count": null, "id": "text-replacement-week-function", "metadata": {}, "outputs": [], "source": ["def process_text_replacement_by_week(week: int, replacements: list, folders: list = None):\\n", "    \\\"\\\"\\\"週別文字替換處理函數\\n", "    \\n", "    Args:\\n", "        week: 週別編號\\n", "        replacements: 替換規則列表 [(old, new), ...]\\n", "        folders: 要處理的資料夾列表，如果為None則處理所有aulink_folder\\n", "    \\\"\\\"\\\"\\n", "    if folders is None:\\n", "        folders = aulink_folder\\n", "    \\n", "    # 取得週別的日期範圍\\n", "    start_date, end_date = get_date_range_for_week(week)\\n", "    print(f\\\"處理週別 W{week}: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}\\\")\\n", "    \\n", "    total_processed = 0\\n", "    total_failures = []\\n", "    \\n", "    for folder in folders:\\n", "        print(f\\\"\\\\n開始處理資料夾: {folder}\\\")\\n", "        \\n", "        # 獲取所有相關月份的檔案\\n", "        all_files = []\\n", "        months_to_check = set()\\n", "        current_date = start_date\\n", "        while current_date <= end_date:\\n", "            months_to_check.add(current_date.strftime('%Y%m'))\\n", "            current_date += <PERSON><PERSON>ta(days=1)\\n", "        \\n", "        print(f\\\"需要檢查的月份: {sorted(months_to_check)}\\\")\\n", "        \\n", "        # 收集所有相關月份的檔案\\n", "        base_folder = Path(fr\\\"\\\\\\\\k5dcnas02\\\\IME\\\\AAS_LOG\\\\{folder}\\\")\\n", "        for month in months_to_check:\\n", "            month_folder_path = base_folder / month\\n", "            if month_folder_path.exists():\\n", "                month_files = list(month_folder_path.glob('*.log'))\\n", "                all_files.extend(month_files)\\n", "                print(f\\\"  月份 {month}: 找到 {len(month_files)} 個檔案\\\")\\n", "            else:\\n", "                print(f\\\"  月份 {month}: 資料夾不存在\\\")\\n", "        \\n", "        if not all_files:\\n", "            print(f\\\"  警告：找不到週別 W{week} 的相關檔案\\\")\\n", "            continue\\n", "        \\n", "        # 過濾出該週的檔案\\n", "        week_files = []\\n", "        for f in all_files:\\n", "            try:\\n", "                # 從檔案名稱中提取日期\\n", "                file_date_str = f.stem.split('_')[-1]\\n", "                file_date = datetime.strptime(file_date_str, '%Y%m%d')\\n", "                if start_date <= file_date <= end_date:\\n", "                    week_files.append(f)\\n", "            except (ValueError, IndexError) as e:\\n", "                print(f\\\"  警告：無法解析檔案日期: {f.name}, 錯誤: {e}\\\")\\n", "                continue\\n", "        \\n", "        print(f\\\"  週別 W{week} 符合的檔案數量: {len(week_files)}\\\")\\n", "        \\n", "        if not week_files:\\n", "            print(f\\\"  警告：週別 W{week} 沒有符合的檔案\\\")\\n", "            continue\\n", "        \\n", "        # 執行文字替換\\n", "        folder_processed = 0\\n", "        folder_failures = []\\n", "        \\n", "        for file_path in week_files:\\n", "            success, result = process_single_file(file_path, replacements)\\n", "            if success:\\n", "                folder_processed += 1\\n", "            else:\\n", "                folder_failures.append(result)\\n", "        \\n", "        # 輸出資料夾處理結果\\n", "        print(f\\\"  {folder} 處理完成: 成功 {folder_processed} 個檔案\\\")\\n", "        \\n", "        if folder_failures:\\n", "            print(f\\\"  處理失敗: {len(folder_failures)} 個檔案\\\")\\n", "            for file_path, error in folder_failures:\\n", "                print(f\\\"    - {file_path}: {error}\\\")\\n", "        \\n", "        total_processed += folder_processed\\n", "        total_failures.extend(folder_failures)\\n", "    \\n", "    # 總結\\n", "    print(f\\\"\\\\n{'='*50}\\\")\\n", "    print(f\\\"週別 W{week} 文字替換處理完成\\\")\\n", "    print(f\\\"總共成功處理: {total_processed} 個檔案\\\")\\n", "    if total_failures:\\n", "        print(f\\\"總共失敗: {len(total_failures)} 個檔案\\\")\\n", "    print(f\\\"{'='*50}\\\")\\n", "    \\n", "    return total_processed, total_failures"]}, {"cell_type": "code", "execution_count": null, "id": "text-replacement-example", "metadata": {}, "outputs": [], "source": ["# 週別文字替換使用範例\\n", "\\n", "# 設定替換規則\\n", "replacements = [\\n", "    ('請按下\\\"\\\"警報復歸\\\"\\\"鈕', \\\"請按下(警報復歸)鈕\\\"),\\n", "    ('請按下警報復歸\\\"鈕\\\"', \\\"請按下(警報復歸)鈕\\\"), \\n", "    ('\\\"請按下\\\"\\\"警報復歸\\\"\\\"鈕\\\"', \\\"請按下(警報復歸)鈕\\\"),\\n", "    (\\\"請按下\\\\\\\"警報復歸\\\\\\\"鈕\\\", \\\"請按下(警報復歸)鈕\\\"),\\n", "    ('\\\"請按下(警報復歸)鈕\\\"', \\\"請按下(警報復歸)鈕\\\")\\n", "]\\n", "\\n", "# 取消註解以下任一行來執行\\n", "\\n", "# 1. 處理當前週別\\n", "# today = datetime.now()\\n", "# today_str = today.strftime('%Y%m%d')\\n", "# current_week = get_week_number(today_str)\\n", "# process_week = current_week - 1  # 處理上一週\\n", "# process_text_replacement_by_week(process_week, replacements)\\n", "\\n", "# 2. 處理特定週別\\n", "# process_text_replacement_by_week(25, replacements)\\n", "\\n", "# 3. 處理特定資料夾的特定週別\\n", "# process_text_replacement_by_week(25, replacements, ['DFLM_HKT-53001'])\\n", "\\n", "# 4. 查看週別對應的日期範圍\\n", "week = 25\\n", "start_date, end_date = get_date_range_for_week(week)\\n", "print(f\\\"週別 W{week}: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}\\\")"]}, {"cell_type": "code", "execution_count": 7, "id": "0adcacf4-dee7-42a8-9213-33d65034368b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-23 11:10:39,214 - INFO - 開始處理週別 W25\n", "2025-06-23 11:10:39,214 - INFO - 找到 6 個檔案\n", "處理檔案: 100%|██████████████████████████████████████████████████████████████████████████| 6/6 [00:04<00:00,  1.22it/s]\n", "2025-06-23 11:10:44,134 - INFO - 合併處理結果...\n", "2025-06-23 11:10:44,149 - INFO - 服務 CheckTime: 10 筆記錄\n", "2025-06-23 11:10:44,149 - INFO - 服務 ChangeEQStatus: 12525 筆記錄\n", "2025-06-23 11:10:44,149 - INFO - 服務 ChangeEQMode: 445 筆記錄\n", "2025-06-23 11:10:44,149 - INFO - 服務 UploadEventCode: 2668 筆記錄\n", "2025-06-23 11:10:44,149 - INFO - 服務 RemoveMLM: 56 筆記錄\n", "2025-06-23 11:10:44,162 - INFO - 服務 UploadRecipe: 28 筆記錄\n", "2025-06-23 11:10:44,163 - INFO - 服務 VerifyMLM: 39 筆記錄\n", "2025-06-23 11:10:44,165 - INFO - 服務 CheckLoader: 72 筆記錄\n", "2025-06-23 11:10:44,165 - INFO - 服務 CheckPanel: 2846 筆記錄\n", "2025-06-23 11:10:44,167 - INFO - 服務 UploadData: 11413 筆記錄\n", "2025-06-23 11:10:44,167 - INFO - 寫入 Excel 檔案: \\\\k5dcnas02\\IME\\個人資料夾\\VICTOR\\對對對對對\\MO攔檢率\\Rowdata\\DFLM_HKT-53001\\DFLM_HKT-53001_25_W25.xlsx\n", "2025-06-23 11:11:02,586 - INFO - 完成處理資料夾: \\\\k5dcnas02\\IME\\AAS_LOG\\DFLM_HKT-53001\n", "2025-06-23 11:11:02,725 - INFO - 開始處理週別 W25\n", "2025-06-23 11:11:02,725 - INFO - 找到 6 個檔案\n", "處理檔案: 100%|██████████████████████████████████████████████████████████████████████████| 6/6 [00:09<00:00,  1.53s/it]\n", "2025-06-23 11:11:11,924 - INFO - 合併處理結果...\n", "2025-06-23 11:11:11,943 - INFO - 服務 ChangeEQStatus: 18969 筆記錄\n", "2025-06-23 11:11:11,943 - INFO - 服務 UploadRecipe: 46 筆記錄\n", "2025-06-23 11:11:11,943 - INFO - 服務 ChangeEQMode: 465 筆記錄\n", "2025-06-23 11:11:11,943 - INFO - 服務 CheckLoader: 226 筆記錄\n", "2025-06-23 11:11:11,943 - INFO - 服務 VerifyMLM: 26 筆記錄\n", "2025-06-23 11:11:11,943 - INFO - 服務 RemoveMLM: 43 筆記錄\n", "2025-06-23 11:11:11,961 - INFO - 服務 UploadEventCode: 5072 筆記錄\n", "2025-06-23 11:11:11,961 - INFO - 服務 CheckPanel: 6053 筆記錄\n", "2025-06-23 11:11:11,961 - INFO - 服務 UploadData: 23889 筆記錄\n", "2025-06-23 11:11:11,964 - INFO - 服務 CheckTime: 6 筆記錄\n", "2025-06-23 11:11:11,964 - INFO - 寫入 Excel 檔案: \\\\k5dcnas02\\IME\\個人資料夾\\VICTOR\\對對對對對\\MO攔檢率\\Rowdata\\DFLM_HKT-53002\\DFLM_HKT-53002_25_W25.xlsx\n", "2025-06-23 11:11:44,203 - INFO - 完成處理資料夾: \\\\k5dcnas02\\IME\\AAS_LOG\\DFLM_HKT-53002\n", "2025-06-23 11:11:44,372 - INFO - 開始處理週別 W25\n", "2025-06-23 11:11:44,372 - INFO - 找到 6 個檔案\n", "處理檔案:  17%|████████████▎                                                             | 1/6 [00:04<00:23,  4.76s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 00:25:44.169 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 00:25:49.606 [Rply]', 'EQ<ITCM-53003-5> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 00:29:48.173 [Rply]', 'EQ<ITCM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 00:29:55.298 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 01:08:40.028 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 01:08:50.012 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 01:09:00.136 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 01:16:17.145 [Rply]', 'EQ<ITCM-53003-4> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 01:25:25.606 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 01:25:31.199 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 01:26:40.598 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 01:27:00.721 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 01:42:42.232 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 01:52:41.448 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Status = ProtocolError, Msg = 遠端伺服器傳回一個錯誤: (400) 不正確的要求。']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 01:52:42.027 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Status = ProtocolError, Msg = 遠端伺服器傳回一個錯誤: (400) 不正確的要求。']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 02:43:22.173 [Rply]', 'EQ<ITCM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 02:43:29.031 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 02:43:34.546 [Rply]', 'EQ<ITCM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 02:43:40.171 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 02:43:45.311 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 02:43:51.013 [Rply]', 'EQ<ITCM-53003-5> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 02:43:56.044 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 02:46:10.743 [Rply]', 'EQ<ITCM-53003-5> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 04:11:10.973 [Rply]', 'EQ<ITCM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 04:14:42.673 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 04:20:44.157 [Rply]', 'EQ<ITCM-53003-1> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 06:08:25.035 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 06:08:30.487 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 06:08:35.893 [Rply]', 'EQ<ITCM-53003-5> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 06:09:02.281 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 06:09:16.232 [Rply]', 'EQ<ITCM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 06:32:30.393 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Status = ProtocolError, Msg = 遠端伺服器傳回一個錯誤: (400) 不正確的要求。']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 06:32:30.940 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Status = ProtocolError, Msg = 遠端伺服器傳回一個錯誤: (400) 不正確的要求。']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 06:44:34.939 [Rply]', 'EQ<ITCM-53003-4> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 06:44:40.642 [Rply]', 'EQ<ITCM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 07:48:41.599 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 07:48:51.441 [Rply]', 'EQ<ITCM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 07:48:57.456 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 07:53:22.256 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 08:36:30.016 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Status = ProtocolError, Msg = 遠端伺服器傳回一個錯誤: (400) 不正確的要求。']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 08:36:30.687 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Status = ProtocolError, Msg = 遠端伺服器傳回一個錯誤: (400) 不正確的要求。']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 08:52:23.490 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 08:52:36.942 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 09:04:03.195 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 09:06:22.299 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 09:10:04.489 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 09:10:10.051 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 09:12:17.131 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Status = ProtocolError, Msg = 遠端伺服器傳回一個錯誤: (400) 不正確的要求。']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 09:12:17.631 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Status = ProtocolError, Msg = 遠端伺服器傳回一個錯誤: (400) 不正確的要求。']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 09:15:52.472 [Rply]', 'EQ<ITCM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 09:16:39.607 [Rply]', 'EQ<ITCM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 09:16:59.824 [Rply]', 'EQ<ITCM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 09:20:18.630 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Status = ProtocolError, Msg = 遠端伺服器傳回一個錯誤: (400) 不正確的要求。']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 09:20:19.146 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Status = ProtocolError, Msg = 遠端伺服器傳回一個錯誤: (400) 不正確的要求。']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 11:32:22.690 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 11:32:28.205 [Rply]', 'EQ<ITCM-53003-4> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 11:32:37.907 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 11:32:53.796 [Rply]', 'EQ<ITCM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 11:33:08.810 [Rply]', 'EQ<ITCM-53003-4> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 13:42:49.525 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 13:46:07.504 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 13:46:18.784 [Rply]', 'EQ<ITCM-53003-5> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 13:46:23.861 [Rply]', 'EQ<ITCM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 13:46:28.892 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:20:06.114 [Rply]', 'EQ<ITCM-53003-4> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:22:55.845 [Rply]', 'EQ<ITCM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:23:01.407 [Rply]', 'EQ<ITCM-53003-1> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:23:36.184 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:23:41.887 [Rply]', 'EQ<ITCM-53003-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:23:52.698 [Rply]', 'EQ<ITCM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:24:13.571 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:24:19.148 [Rply]', 'EQ<ITCM-53003-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:24:25.960 [Rply]', 'EQ<ITCM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:24:50.989 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:25:02.378 [Rply]', 'EQ<ITCM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:25:26.829 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:25:32.375 [Rply]', 'EQ<ITCM-53003-4> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:25:40.624 [Rply]', 'EQ<ITCM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:26:01.497 [Rply]', 'EQ<ITCM-53003-4> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:26:18.323 [Rply]', 'EQ<ITCM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:30:04.736 [Rply]', 'EQ<ITCM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:30:27.796 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:30:33.545 [Rply]', 'EQ<ITCM-53003-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:30:40.966 [Rply]', 'EQ<ITCM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:31:04.886 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:31:43.022 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:31:48.928 [Rply]', 'EQ<ITCM-53003-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:58:44.106 [Rply]', 'EQ<ITCM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:58:50.574 [Rply]', 'EQ<ITCM-53003-5> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 16:02:43.658 [Rply]', 'EQ<ITCM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 16:42:18.415 [Rply]', 'EQ<ITCM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 16:42:23.602 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 16:42:29.086 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 16:42:34.632 [Rply]', 'EQ<ITCM-53003-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 17:24:02.913 [Rply]', 'EQ<ITCM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 17:24:07.975 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 17:24:26.926 [Rply]', 'EQ<ITCM-53003-5> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 17:24:31.957 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 17:24:37.035 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 17:42:20.919 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 17:47:07.325 [Rply]', 'EQ<ITCM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 17:47:16.074 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 17:47:21.511 [Rply]', 'EQ<ITCM-53003-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 17:47:26.807 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 18:21:36.433 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 18:21:51.119 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 18:23:51.793 [Rply]', 'EQ<ITCM-53003-5> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 18:24:06.901 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 18:24:11.963 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 18:24:17.603 [Rply]', 'EQ<ITCM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 18:59:36.929 [Rply]', 'EQ<ITCM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 18:59:47.413 [Rply]', 'EQ<ITCM-53003-5> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 19:00:30.986 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 19:19:15.515 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 19:20:05.212 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 19:50:30.107 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 20:09:02.684 [Rply]', 'EQ<ITCM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 20:09:08.184 [Rply]', 'EQ<ITCM-53003-5> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 20:09:13.246 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 20:09:19.323 [Rply]', 'EQ<ITCM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 20:13:43.935 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 20:13:49.419 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 20:13:55.262 [Rply]', 'EQ<ITCM-53003-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 20:23:24.372 [Rply]', 'EQ<ITCM-53003-5> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 20:23:29.434 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理檔案:  33%|████████████████████████▋                                                 | 2/6 [00:07<00:14,  3.58s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 20:49:33.638 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 20:51:55.279 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 20:52:00.841 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 20:52:06.387 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 20:52:11.934 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:21:05.446 [Rply]', 'EQ<ITCM-53003-4> Func<UploadRecipe> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:30:48.101 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:30:54.101 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:31:05.302 [Rply]', 'EQ<ITCM-53003-4> Func<UploadRecipe> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:31:55.641 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:36:31.767 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:37:35.026 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:37:40.931 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:37:58.882 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:38:04.476 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 22:08:18.908 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 22:08:24.454 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 22:32:23.094 [Rply]', 'EQ<ITCM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 22:32:31.281 [Rply]', 'EQ<ITCM-53003-5> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 22:32:36.749 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 22:43:54.847 [Rply]', 'EQ<ITCM-53003-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 22:54:23.966 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 22:56:34.061 [Rply]', 'EQ<ITCM-53003-5> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:02:11.399 [Rply]', 'EQ<ITCM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:04:22.760 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:31:26.812 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:31:34.577 [Rply]', 'EQ<ITCM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:33:19.816 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:33:27.909 [Rply]', 'EQ<ITCM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:33:33.017 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:33:38.642 [Rply]', 'EQ<ITCM-53003-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:33:43.719 [Rply]', 'EQ<ITCM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:33:57.280 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:34:04.405 [Rply]', 'EQ<ITCM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:34:09.904 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:34:15.341 [Rply]', 'EQ<ITCM-53003-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:34:20.387 [Rply]', 'EQ<ITCM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:34:34.808 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:43:08.705 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:43:14.376 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:43:19.485 [Rply]', 'EQ<ITCM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:43:24.672 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:43:30.124 [Rply]', 'EQ<ITCM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:43:40.123 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:56:11.526 [Rply]', 'EQ<ITCM-53003-5> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:00:00.688 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:23:33.842 [Rply]', 'EQ<ITCM-53003-1> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:35:16.642 [Rply]', 'EQ<ITCM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:35:23.672 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:35:35.608 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:37:23.487 [Rply]', 'EQ<ITCM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:37:28.627 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:37:34.548 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:37:40.517 [Rply]', 'EQ<ITCM-53003-5> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:37:59.999 [Rply]', 'EQ<ITCM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:38:05.201 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:47:09.001 [Rply]', 'EQ<ITCM-53003-5> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:47:14.079 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:48:18.853 [Rply]', 'EQ<ITCM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:04:27.508 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:04:32.898 [Rply]', 'EQ<ITCM-53003-5> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:04:43.131 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:04:48.771 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:04:54.302 [Rply]', 'EQ<ITCM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:09:27.892 [Rply]', 'EQ<ITCM-53003-5> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:18:17.818 [Rply]', 'EQ<ITCM-53003-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:18:22.880 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:18:31.489 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:18:36.972 [Rply]', 'EQ<ITCM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:37:03.378 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:37:10.752 [Rply]', 'EQ<ITCM-53003-5> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:37:15.830 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:37:21.329 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:37:39.843 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:50:11.948 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:08:08.269 [Rply]', 'EQ<ITCM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:20:11.815 [Rply]', 'EQ<ITCM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:30:42.403 [Rply]', 'EQ<ITCM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:30:47.558 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:30:57.104 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:44:37.488 [Rply]', 'EQ<ITCM-53003-4> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:45:03.486 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:49:39.377 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:49:44.971 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:54:53.859 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:03:17.975 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:03:51.284 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:03:58.018 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:04:04.908 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:04:10.423 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:06:07.379 [Rply]', 'EQ<ITCM-53003-2> Func<UploadRecipe> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:08:58.079 [Rply]', 'EQ<ITCM-53003-2> Func<UploadRecipe> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:13:03.943 [Rply]', 'EQ<ITCM-53003-2> Func<UploadRecipe> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:19:43.759 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:27:16.147 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:36:48.210 [Rply]', 'EQ<ITCM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:01:23.138 [Rply]', 'EQ<ITCM-53003-2> Func<UploadRecipe> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:02:19.882 [Rply]', 'EQ<ITCM-53003-1> Func<CheckLoader> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:02:25.428 [Rply]', 'EQ<ITCM-53003-1> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:02:30.990 [Rply]', 'EQ<ITCM-53003-1> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:02:39.567 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:03:05.759 [Rply]', 'EQ<ITCM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:20:51.690 [Rply]', 'EQ<ITCM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:22:25.445 [Rply]', 'EQ<ITCM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:22:31.101 [Rply]', 'EQ<ITCM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:55:47.389 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:56:20.572 [Rply]', 'EQ<ITCM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:56:25.634 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:56:31.068 [Rply]', 'EQ<ITCM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:57:08.298 [Rply]', 'EQ<ITCM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:57:16.750 [Rply]', 'EQ<ITCM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:57:22.875 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:15:51.676 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:19:51.134 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:19:56.587 [Rply]', 'EQ<ITCM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:20:01.711 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:20:07.320 [Rply]', 'EQ<ITCM-53003-5> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:20:17.116 [Rply]', 'EQ<ITCM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:29:13.643 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:30:40.337 [Rply]', 'EQ<ITCM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:30:45.836 [Rply]', 'EQ<ITCM-53003-1> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:30:50.898 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:30:56.440 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:31:05.127 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:31:10.376 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:31:16.173 [Rply]', 'EQ<ITCM-53003-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:40:34.671 [Rply]', 'EQ<ITCM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:40:40.123 [Rply]', 'EQ<ITCM-53003-5> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:41:42.692 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:41:51.734 [Rply]', 'EQ<ITCM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:51:18.383 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:51:23.836 [Rply]', 'EQ<ITCM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:51:30.601 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 06:01:28.239 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 06:01:33.972 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 06:12:43.009 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 06:13:33.160 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 06:23:45.541 [Rply]', 'EQ<ITCM-53003-5> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 06:23:51.165 [Rply]', 'EQ<ITCM-53003-5> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 06:23:57.008 [Rply]', 'EQ<ITCM-53003-5> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 06:24:23.720 [Rply]', 'EQ<ITCM-53003-5> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 06:24:31.340 [Rply]', 'EQ<ITCM-53003-5> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 06:28:32.668 [Rply]', 'EQ<ITCM-53003-4> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理檔案:  50%|█████████████████████████████████████                                     | 3/6 [00:09<00:08,  2.97s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 07:11:19.328 [Rply]', 'EQ<ITCM-53003-5> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 07:40:39.819 [Rply]', 'EQ<ITCM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:03:54.635 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:09:15.428 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:09:30.954 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:09:51.108 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:12:43.089 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:17:24.055 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:38:54.850 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:39:00.428 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:40:08.530 [Rply]', 'EQ<ITCM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:45:07.098 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:45:14.504 [Rply]', 'EQ<ITCM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:45:20.034 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:53:08.061 [Rply]', 'EQ<ITCM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:41:52.868 [Rply]', 'EQ<ITCM-53003-5> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:41:58.362 [Rply]', 'EQ<ITCM-53003-5> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 13:40:25.036 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 13:43:38.781 [Rply]', 'EQ<ITCM-53003-4> Func<UploadRecipe> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 13:44:07.699 [Rply]', 'EQ<ITCM-53003-4> Func<UploadRecipe> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 13:48:50.684 [Rply]', 'EQ<ITCM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:17:17.302 [Rply]', 'EQ<ITCM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:17:27.910 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:17:33.488 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:52:20.594 [Rply]', 'EQ<ITCM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:29:31.240 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:29:37.068 [Rply]', 'EQ<ITCM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:30:31.640 [Rply]', 'EQ<ITCM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:30:37.667 [Rply]', 'EQ<ITCM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:31:04.242 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:31:10.054 [Rply]', 'EQ<ITCM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:31:15.639 [Rply]', 'EQ<ITCM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:31:21.338 [Rply]', 'EQ<ITCM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:32:04.927 [Rply]', 'EQ<ITCM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:32:10.755 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 23:15:56.619 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Status = ProtocolError, Msg = 遠端伺服器傳回一個錯誤: (400) 不正確的要求。']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 23:15:57.088 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Status = ProtocolError, Msg = 遠端伺服器傳回一個錯誤: (400) 不正確的要求。']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 23:51:23.765 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Status = ProtocolError, Msg = 遠端伺服器傳回一個錯誤: (400) 不正確的要求。']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 23:51:24.262 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Status = ProtocolError, Msg = 遠端伺服器傳回一個錯誤: (400) 不正確的要求。']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/21 06:37:40.652 [Rply]', 'EQ<ITCM-53003-5> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/21 06:38:23.483 [Rply]', 'EQ<ITCM-53003-4> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/21 19:42:08.206 [Rply]', 'EQ<ITCM-53003-5> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Status = ProtocolError, Msg = 遠端伺服器傳回一個錯誤: (400) 不正確的要求。']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/21 19:42:08.753 [Rply]', 'EQ<ITCM-53003-5> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Status = ProtocolError, Msg = 遠端伺服器傳回一個錯誤: (400) 不正確的要求。']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理檔案:  67%|█████████████████████████████████████████████████▎                        | 4/6 [00:15<00:07,  3.91s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 10:41:50.248 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 10:41:56.841 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 10:51:02.089 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 10:51:11.447 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 11:16:28.443 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 14:14:27.260 [Rply]', 'EQ<ITCM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 14:14:32.842 [Rply]', 'EQ<ITCM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 14:14:38.404 [Rply]', 'EQ<ITCM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 14:14:51.262 [Rply]', 'EQ<ITCM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 14:18:02.241 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 14:18:07.647 [Rply]', 'EQ<ITCM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 14:29:18.506 [Rply]', 'EQ<ITCM-53003-4> Func<UploadRecipe> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 14:33:05.938 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 14:33:15.000 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 14:35:39.343 [Rply]', 'EQ<ITCM-53003-4> Func<UploadRecipe> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 14:46:00.610 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 14:57:00.544 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 15:15:57.701 [Rply]', 'EQ<ITCM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 15:16:43.208 [Rply]', 'EQ<ITCM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 15:17:06.210 [Rply]', 'EQ<ITCM-53003-5> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 15:17:24.265 [Rply]', 'EQ<ITCM-53003-3> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 15:41:19.194 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 16:02:04.584 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 16:02:10.884 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 16:30:34.278 [Rply]', 'EQ<ITCM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 16:30:39.965 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 17:00:28.827 [Rply]', 'EQ<ITCM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 17:00:49.856 [Rply]', 'EQ<ITCM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 18:09:42.604 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 18:10:06.961 [Rply]', 'EQ<ITCM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 18:10:12.445 [Rply]', 'EQ<ITCM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 18:28:10.968 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 18:28:16.780 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 19:39:04.042 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 19:43:47.730 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 19:45:10.033 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 19:45:34.687 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 19:47:09.770 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 19:53:52.226 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 20:01:18.443 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 20:01:23.958 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 20:04:20.533 [Rply]', 'EQ<ITCM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 20:10:14.869 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 20:19:19.945 [Rply]', 'EQ<ITCM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 20:20:57.310 [Rply]', 'EQ<ITCM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 20:21:02.809 [Rply]', 'EQ<ITCM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 20:21:08.418 [Rply]', 'EQ<ITCM-53003-1> Func<CheckLoader> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 20:29:55.262 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 20:31:53.092 [Rply]', 'EQ<ITCM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 20:34:02.203 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 20:46:07.707 [Rply]', 'EQ<ITCM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 20:50:55.707 [Rply]', 'EQ<ITCM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 20:51:01.254 [Rply]', 'EQ<ITCM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 21:10:47.124 [Rply]', 'EQ<ITCM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/18 22:23:37.513 [Rply]', 'EQ<ITCM-53003-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理檔案: 100%|██████████████████████████████████████████████████████████████████████████| 6/6 [00:28<00:00,  4.78s/it]\n", "2025-06-23 11:12:13,093 - INFO - 合併處理結果...\n", "2025-06-23 11:12:13,150 - INFO - 服務 ChangeEQStatus: 14722 筆記錄\n", "2025-06-23 11:12:13,151 - INFO - 服務 CheckLoader: 257 筆記錄\n", "2025-06-23 11:12:13,152 - INFO - 服務 ChangeEQMode: 1497 筆記錄\n", "2025-06-23 11:12:13,152 - INFO - 服務 UploadRecipe: 1114 筆記錄\n", "2025-06-23 11:12:13,154 - INFO - 服務 UploadEventCode: 72280 筆記錄\n", "2025-06-23 11:12:13,155 - INFO - 服務 VerifyMLM: 32 筆記錄\n", "2025-06-23 11:12:13,157 - INFO - 服務 RemoveMLM: 41 筆記錄\n", "2025-06-23 11:12:13,159 - INFO - 服務 UploadData: 21399 筆記錄\n", "2025-06-23 11:12:13,159 - INFO - 服務 CheckPanel: 5090 筆記錄\n", "2025-06-23 11:12:13,159 - INFO - 服務 CheckTime: 7 筆記錄\n", "2025-06-23 11:12:13,162 - INFO - 寫入 Excel 檔案: \\\\k5dcnas02\\IME\\個人資料夾\\VICTOR\\對對對對對\\MO攔檢率\\Rowdata\\DFLM_HKT-53003\\DFLM_HKT-53003_25_W25.xlsx\n", "2025-06-23 11:13:42,298 - INFO - 完成處理資料夾: \\\\k5dcnas02\\IME\\AAS_LOG\\DFLM_HKT-53003\n", "2025-06-23 11:13:42,618 - INFO - 開始處理週別 W25\n", "2025-06-23 11:13:42,618 - INFO - 找到 6 個檔案\n", "處理檔案: 100%|██████████████████████████████████████████████████████████████████████████| 6/6 [00:05<00:00,  1.19it/s]\n", "2025-06-23 11:13:47,659 - INFO - 合併處理結果...\n", "2025-06-23 11:13:47,674 - INFO - 服務 ChangeEQStatus: 2572 筆記錄\n", "2025-06-23 11:13:47,675 - INFO - 服務 CheckLoader: 232 筆記錄\n", "2025-06-23 11:13:47,676 - INFO - 服務 UploadEventCode: 4903 筆記錄\n", "2025-06-23 11:13:47,677 - INFO - 服務 UploadRecipe: 33 筆記錄\n", "2025-06-23 11:13:47,678 - INFO - 服務 CheckTime: 27 筆記錄\n", "2025-06-23 11:13:47,680 - INFO - 服務 CheckPanel: 5637 筆記錄\n", "2025-06-23 11:13:47,681 - INFO - 服務 UploadData: 16906 筆記錄\n", "2025-06-23 11:13:47,683 - INFO - 寫入 Excel 檔案: \\\\k5dcnas02\\IME\\個人資料夾\\VICTOR\\對對對對對\\MO攔檢率\\Rowdata\\DV_V_AP-53201\\DV_V_AP-53201_25_W25.xlsx\n", "2025-06-23 11:14:06,454 - INFO - 完成處理資料夾: \\\\k5dcnas02\\IME\\AAS_LOG\\DV_V_AP-53201\n", "2025-06-23 11:14:06,669 - INFO - 開始處理週別 W25\n", "2025-06-23 11:14:06,669 - INFO - 找到 6 個檔案\n", "處理檔案: 100%|██████████████████████████████████████████████████████████████████████████| 6/6 [00:04<00:00,  1.30it/s]\n", "2025-06-23 11:14:11,302 - INFO - 合併處理結果...\n", "2025-06-23 11:14:11,325 - INFO - 服務 ChangeEQStatus: 2602 筆記錄\n", "2025-06-23 11:14:11,326 - INFO - 服務 ChangeEQMode: 55 筆記錄\n", "2025-06-23 11:14:11,328 - INFO - 服務 CheckLoader: 217 筆記錄\n", "2025-06-23 11:14:11,329 - INFO - 服務 UploadEventCode: 5406 筆記錄\n", "2025-06-23 11:14:11,331 - INFO - 服務 UploadRecipe: 31 筆記錄\n", "2025-06-23 11:14:11,333 - INFO - 服務 CheckPanel: 4809 筆記錄\n", "2025-06-23 11:14:11,334 - INFO - 服務 UploadData: 14955 筆記錄\n", "2025-06-23 11:14:11,336 - INFO - 服務 CheckTime: 40 筆記錄\n", "2025-06-23 11:14:11,337 - INFO - 寫入 Excel 檔案: \\\\k5dcnas02\\IME\\個人資料夾\\VICTOR\\對對對對對\\MO攔檢率\\Rowdata\\DV_V_AP-53202\\DV_V_AP-53202_25_W25.xlsx\n", "2025-06-23 11:14:28,810 - INFO - 完成處理資料夾: \\\\k5dcnas02\\IME\\AAS_LOG\\DV_V_AP-53202\n", "2025-06-23 11:14:28,985 - INFO - 開始處理週別 W25\n", "2025-06-23 11:14:28,985 - INFO - 找到 6 個檔案\n", "處理檔案: 100%|██████████████████████████████████████████████████████████████████████████| 6/6 [00:02<00:00,  2.66it/s]\n", "2025-06-23 11:14:31,263 - INFO - 合併處理結果...\n", "2025-06-23 11:14:31,275 - INFO - 服務 ChangeEQStatus: 4751 筆記錄\n", "2025-06-23 11:14:31,276 - INFO - 服務 CheckPanel: 2039 筆記錄\n", "2025-06-23 11:14:31,277 - INFO - 服務 UploadData: 5939 筆記錄\n", "2025-06-23 11:14:31,278 - INFO - 服務 CheckLoader: 148 筆記錄\n", "2025-06-23 11:14:31,278 - INFO - 服務 ChangeEQMode: 345 筆記錄\n", "2025-06-23 11:14:31,279 - INFO - 服務 UploadRecipe: 18 筆記錄\n", "2025-06-23 11:14:31,280 - INFO - 服務 UploadEventCode: 1775 筆記錄\n", "2025-06-23 11:14:31,281 - INFO - 服務 CheckTime: 29 筆記錄\n", "2025-06-23 11:14:31,282 - INFO - 寫入 Excel 檔案: \\\\k5dcnas02\\IME\\個人資料夾\\VICTOR\\對對對對對\\MO攔檢率\\Rowdata\\DV_V_AP-53301\\DV_V_AP-53301_25_W25.xlsx\n", "2025-06-23 11:14:40,597 - INFO - 完成處理資料夾: \\\\k5dcnas02\\IME\\AAS_LOG\\DV_V_AP-53301\n", "2025-06-23 11:14:40,748 - INFO - 開始處理週別 W25\n", "2025-06-23 11:14:40,748 - INFO - 找到 6 個檔案\n", "處理檔案:  33%|████████████████████████▋                                                 | 2/6 [00:01<00:02,  1.75it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 14:50:26.508 [Rply]', 'EQ<LA_NM-53001-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:03:52.845 [Rply]', 'EQ<LA_NM-53001-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:03:57.954 [Rply]', 'EQ<LA_NM-53001-4> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:04:03.078 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 20:14:42.825 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 20:14:54.152 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 20:17:36.183 [Rply]', 'EQ<LA_NM-53001-3> Func<UploadRecipe> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 20:41:09.549 [Rply]', 'EQ<LA_NM-53001-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 20:41:14.705 [Rply]', 'EQ<LA_NM-53001-3> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 20:55:49.643 [Rply]', 'EQ<LA_NM-53001-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:41:08.335 [Rply]', 'EQ<LA_NM-53001-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:04:48.590 [Rply]', 'EQ<LA_NM-53001-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:15:59.503 [Rply]', 'EQ<LA_NM-53001-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:19:17.555 [Rply]', 'EQ<LA_NM-53001-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理檔案:  67%|█████████████████████████████████████████████████▎                        | 4/6 [00:01<00:00,  2.53it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:12:23.858 [Rply]', 'EQ<LA_NM-53001-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:12:31.685 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:12:37.076 [Rply]', 'EQ<LA_NM-53001-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:19:09.862 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:19:17.174 [Rply]', 'EQ<LA_NM-53001-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:19:22.236 [Rply]', 'EQ<LA_NM-53001-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:19:35.781 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:20:09.278 [Rply]', 'EQ<LA_NM-53001-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:20:20.542 [Rply]', 'EQ<LA_NM-53001-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:20:28.901 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:33:22.162 [Rply]', 'EQ<LA_NM-53001-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:33:59.861 [Rply]', 'EQ<LA_NM-53001-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:34:09.672 [Rply]', 'EQ<LA_NM-53001-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:34:52.465 [Rply]', 'EQ<LA_NM-53001-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:36:32.298 [Rply]', 'EQ<LA_NM-53001-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:03:17.685 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:30:05.109 [Rply]', 'EQ<LA_NM-53001-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:30:19.576 [Rply]', 'EQ<LA_NM-53001-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:48:34.752 [Rply]', 'EQ<LA_NM-53001-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:27:49.784 [Rply]', 'EQ<LA_NM-53001-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:27:54.893 [Rply]', 'EQ<LA_NM-53001-1> Func<CheckLoader> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:33:06.485 [Rply]', 'EQ<LA_NM-53001-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:36:19.730 [Rply]', 'EQ<LA_NM-53001-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:42:36.080 [Rply]', 'EQ<LA_NM-53001-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:42:41.220 [Rply]', 'EQ<LA_NM-53001-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:07:31.672 [Rply]', 'EQ<LA_NM-53001-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:07:37.124 [Rply]', 'EQ<LA_NM-53001-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:07:46.483 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:07:53.966 [Rply]', 'EQ<LA_NM-53001-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:08:13.355 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:10:06.734 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:10:21.123 [Rply]', 'EQ<LA_NM-53001-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:30:55.976 [Rply]', 'EQ<LA_NM-53001-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:37:48.402 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 07:02:26.801 [Rply]', 'EQ<LA_NM-53001-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 07:02:32.270 [Rply]', 'EQ<LA_NM-53001-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 07:02:41.690 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 07:02:49.049 [Rply]', 'EQ<LA_NM-53001-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 07:03:06.875 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 07:07:28.692 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 07:07:55.986 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 07:08:28.389 [Rply]', 'EQ<LA_NM-53001-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 07:30:25.734 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 07:30:30.859 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:52:02.647 [Rply]', 'EQ<LA_NM-53001-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:52:07.787 [Rply]', 'EQ<LA_NM-53001-4> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:52:12.911 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:54:54.238 [Rply]', 'EQ<LA_NM-53001-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:55:30.812 [Rply]', 'EQ<LA_NM-53001-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:57:33.065 [Rply]', 'EQ<LA_NM-53001-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:57:38.221 [Rply]', 'EQ<LA_NM-53001-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 09:06:40.017 [Rply]', 'EQ<LA_NM-53001-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 09:11:07.598 [Rply]', 'EQ<LA_NM-53001-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 09:18:59.712 [Rply]', 'EQ<LA_NM-53001-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 09:26:32.305 [Rply]', 'EQ<LA_NM-53001-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 10:01:43.623 [Rply]', 'EQ<LA_NM-53001-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 10:02:53.772 [Rply]', 'EQ<LA_NM-53001-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 10:03:13.864 [Rply]', 'EQ<LA_NM-53001-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 10:04:11.029 [Rply]', 'EQ<LA_NM-53001-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:22:55.413 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:23:13.943 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:23:33.972 [Rply]', 'EQ<LA_NM-53001-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:33:07.957 [Rply]', 'EQ<LA_NM-53001-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:35:47.315 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:36:20.186 [Rply]', 'EQ<LA_NM-53001-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:07:41.878 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:10:00.722 [Rply]', 'EQ<LA_NM-53001-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:10:09.518 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:28:02.915 [Rply]', 'EQ<LA_NM-53001-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:28:22.522 [Rply]', 'EQ<LA_NM-53001-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:29:13.892 [Rply]', 'EQ<LA_NM-53001-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:29:19.126 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:29:32.968 [Rply]', 'EQ<LA_NM-53001-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:36:21.331 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:36:39.454 [Rply]', 'EQ<LA_NM-53001-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 13:40:46.823 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 13:40:56.306 [Rply]', 'EQ<LA_NM-53001-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 13:41:05.540 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 13:46:43.149 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 13:46:56.913 [Rply]', 'EQ<LA_NM-53001-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 13:47:07.427 [Rply]', 'EQ<LA_NM-53001-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:05:43.621 [Rply]', 'EQ<LA_NM-53001-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:05:50.886 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:05:55.917 [Rply]', 'EQ<LA_NM-53001-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:06:01.041 [Rply]', 'EQ<LA_NM-53001-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:07:29.157 [Rply]', 'EQ<LA_NM-53001-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:07:34.297 [Rply]', 'EQ<LA_NM-53001-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:22:13.392 [Rply]', 'EQ<LA_NM-53001-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:51:44.996 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:01:20.136 [Rply]', 'EQ<LA_NM-53001-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:01:37.665 [Rply]', 'EQ<LA_NM-53001-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:02:12.302 [Rply]', 'EQ<LA_NM-53001-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:02:17.942 [Rply]', 'EQ<LA_NM-53001-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:02:34.065 [Rply]', 'EQ<LA_NM-53001-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:15:17.859 [Rply]', 'EQ<LA_NM-53001-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:15:36.967 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:15:41.998 [Rply]', 'EQ<LA_NM-53001-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:15:47.122 [Rply]', 'EQ<LA_NM-53001-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:15:53.887 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:16:01.402 [Rply]', 'EQ<LA_NM-53001-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:16:18.931 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:16:52.349 [Rply]', 'EQ<LA_NM-53001-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:17:12.504 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:17:17.534 [Rply]', 'EQ<LA_NM-53001-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:17:23.096 [Rply]', 'EQ<LA_NM-53001-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:17:29.361 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:17:37.220 [Rply]', 'EQ<LA_NM-53001-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:17:54.234 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:18:28.511 [Rply]', 'EQ<LA_NM-53001-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:18:47.915 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:18:52.930 [Rply]', 'EQ<LA_NM-53001-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:18:58.196 [Rply]', 'EQ<LA_NM-53001-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:19:05.492 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:19:12.960 [Rply]', 'EQ<LA_NM-53001-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:19:29.989 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:19:40.129 [Rply]', 'EQ<LA_NM-53001-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:19:48.862 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:19:53.971 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:20:04.720 [Rply]', 'EQ<LA_NM-53001-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:20:24.249 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:20:29.280 [Rply]', 'EQ<LA_NM-53001-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:20:34.498 [Rply]', 'EQ<LA_NM-53001-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:20:48.949 [Rply]', 'EQ<LA_NM-53001-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:20:57.183 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:21:22.133 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:21:32.539 [Rply]', 'EQ<LA_NM-53001-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:21:40.194 [Rply]', 'EQ<LA_NM-53001-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:22:02.301 [Rply]', 'EQ<LA_NM-53001-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:22:07.426 [Rply]', 'EQ<LA_NM-53001-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:22:15.753 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:22:20.877 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:22:25.986 [Rply]', 'EQ<LA_NM-53001-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:22:41.422 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:23:15.621 [Rply]', 'EQ<LA_NM-53001-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:23:36.104 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:23:41.134 [Rply]', 'EQ<LA_NM-53001-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:23:46.493 [Rply]', 'EQ<LA_NM-53001-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:23:52.243 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:23:59.429 [Rply]', 'EQ<LA_NM-53001-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:24:17.365 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:24:52.127 [Rply]', 'EQ<LA_NM-53001-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:25:11.250 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:25:16.546 [Rply]', 'EQ<LA_NM-53001-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:25:21.671 [Rply]', 'EQ<LA_NM-53001-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:25:27.904 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:25:35.622 [Rply]', 'EQ<LA_NM-53001-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:25:53.167 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:26:26.851 [Rply]', 'EQ<LA_NM-53001-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:26:46.990 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:26:52.020 [Rply]', 'EQ<LA_NM-53001-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:26:57.160 [Rply]', 'EQ<LA_NM-53001-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:27:03.394 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:27:11.081 [Rply]', 'EQ<LA_NM-53001-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:27:28.610 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:28:03.763 [Rply]', 'EQ<LA_NM-53001-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:28:22.573 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:28:27.588 [Rply]', 'EQ<LA_NM-53001-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:28:32.885 [Rply]', 'EQ<LA_NM-53001-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:28:39.134 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:28:47.102 [Rply]', 'EQ<LA_NM-53001-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:29:04.178 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:29:39.175 [Rply]', 'EQ<LA_NM-53001-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:29:58.282 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:30:03.422 [Rply]', 'EQ<LA_NM-53001-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:30:08.546 [Rply]', 'EQ<LA_NM-53001-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:30:23.920 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:30:39.168 [Rply]', 'EQ<LA_NM-53001-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:30:44.465 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:31:35.225 [Rply]', 'EQ<LA_NM-53001-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:31:45.302 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:31:50.364 [Rply]', 'EQ<LA_NM-53001-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:32:00.878 [Rply]', 'EQ<LA_NM-53001-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:32:05.935 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:32:10.966 [Rply]', 'EQ<LA_NM-53001-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:52:33.600 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 16:53:37.208 [Rply]', 'EQ<LA_NM-53001-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 16:54:10.173 [Rply]', 'EQ<LA_NM-53001-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理檔案:  83%|█████████████████████████████████████████████████████████████▋            | 5/6 [00:03<00:00,  1.13it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/21 05:30:36.786 [Rply]', 'EQ<LA_NM-53001-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/21 06:37:38.887 [Rply]', 'EQ<LA_NM-53001-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/21 08:19:25.090 [Rply]', 'EQ<LA_NM-53001-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/21 08:59:50.000 [Rply]', 'EQ<LA_NM-53001-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/21 09:04:49.312 [Rply]', 'EQ<LA_NM-53001-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/21 09:07:15.671 [Rply]', 'EQ<LA_NM-53001-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/21 09:54:15.695 [Rply]', 'EQ<LA_NM-53001-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理檔案: 100%|██████████████████████████████████████████████████████████████████████████| 6/6 [00:04<00:00,  1.44it/s]\n", "2025-06-23 11:14:44,918 - INFO - 合併處理結果...\n", "2025-06-23 11:14:44,926 - INFO - 服務 ChangeEQStatus: 3676 筆記錄\n", "2025-06-23 11:14:44,926 - INFO - 服務 CheckLoader: 199 筆記錄\n", "2025-06-23 11:14:44,926 - INFO - 服務 UploadRecipe: 17 筆記錄\n", "2025-06-23 11:14:44,926 - INFO - 服務 VerifyMLM: 59 筆記錄\n", "2025-06-23 11:14:44,926 - INFO - 服務 RemoveMLM: 80 筆記錄\n", "2025-06-23 11:14:44,926 - INFO - 服務 ChangeEQMode: 681 筆記錄\n", "2025-06-23 11:14:44,926 - INFO - 服務 UploadEventCode: 5671 筆記錄\n", "2025-06-23 11:14:44,926 - INFO - 服務 CheckTime: 27 筆記錄\n", "2025-06-23 11:14:44,926 - INFO - 服務 CheckPanel: 2337 筆記錄\n", "2025-06-23 11:14:44,926 - INFO - 服務 UploadData: 9265 筆記錄\n", "2025-06-23 11:14:44,941 - INFO - 寫入 Excel 檔案: \\\\k5dcnas02\\IME\\個人資料夾\\VICTOR\\對對對對對\\MO攔檢率\\Rowdata\\LA_NM-53001\\LA_NM-53001_25_W25.xlsx\n", "2025-06-23 11:14:59,631 - INFO - 完成處理資料夾: \\\\k5dcnas02\\IME\\AAS_LOG\\LA_NM-53001\n", "2025-06-23 11:14:59,840 - INFO - 開始處理週別 W25\n", "2025-06-23 11:14:59,840 - INFO - 找到 6 個檔案\n", "處理檔案:  33%|████████████████████████▋                                                 | 2/6 [00:01<00:01,  2.31it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:27:54.354 [Rply]', 'EQ<LA_NM-53002-3> Func<UploadRecipe> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:27:59.526 [Rply]', 'EQ<LA_NM-53002-3> Func<UploadRecipe> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:52:57.277 [Rply]', 'EQ<LA_NM-53002-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 15:53:02.495 [Rply]', 'EQ<LA_NM-53002-3> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 17:32:54.694 [Rply]', 'EQ<LA_NM-53002-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 17:41:15.859 [Rply]', 'EQ<LA_NM-53002-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 17:41:20.999 [Rply]', 'EQ<LA_NM-53002-3> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 17:49:52.412 [Rply]', 'EQ<LA_NM-53002-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 17:49:57.552 [Rply]', 'EQ<LA_NM-53002-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 17:50:02.598 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 18:35:36.059 [Rply]', 'EQ<LA_NM-53002-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 18:38:16.890 [Rply]', 'EQ<LA_NM-53002-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 19:01:25.303 [Rply]', 'EQ<LA_NM-53002-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 19:31:38.030 [Rply]', 'EQ<LA_NM-53002-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 19:33:37.610 [Rply]', 'EQ<LA_NM-53002-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 19:40:45.507 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 19:44:02.939 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 19:45:41.366 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:01:12.882 [Rply]', 'EQ<LA_NM-53002-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:04:14.917 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:11:46.013 [Rply]', 'EQ<LA_NM-53002-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:11:51.185 [Rply]', 'EQ<LA_NM-53002-2> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:32:38.696 [Rply]', 'EQ<LA_NM-53002-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:34:01.171 [Rply]', 'EQ<LA_NM-53002-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:35:32.911 [Rply]', 'EQ<LA_NM-53002-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:46:07.370 [Rply]', 'EQ<LA_NM-53002-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 22:47:43.353 [Rply]', 'EQ<LA_NM-53002-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 22:47:48.478 [Rply]', 'EQ<LA_NM-53002-1> Func<CheckLoader> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 22:47:53.587 [Rply]', 'EQ<LA_NM-53002-1> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 22:47:58.696 [Rply]', 'EQ<LA_NM-53002-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理檔案:  83%|█████████████████████████████████████████████████████████████▋            | 5/6 [00:06<00:01,  1.76s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:17:19.119 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:07:43.755 [Rply]', 'EQ<LA_NM-53002-1> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:07:54.598 [Rply]', 'EQ<LA_NM-53002-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:08:39.483 [Rply]', 'EQ<LA_NM-53002-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:08:44.545 [Rply]', 'EQ<LA_NM-53002-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:08:49.670 [Rply]', 'EQ<LA_NM-53002-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:08:54.779 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:31:50.276 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:37:31.754 [Rply]', 'EQ<LA_NM-53002-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:37:36.801 [Rply]', 'EQ<LA_NM-53002-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:37:41.910 [Rply]', 'EQ<LA_NM-53002-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:37:51.312 [Rply]', 'EQ<LA_NM-53002-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:26:21.992 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:27:24.344 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:27:44.905 [Rply]', 'EQ<LA_NM-53002-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:28:17.323 [Rply]', 'EQ<LA_NM-53002-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:29:16.098 [Rply]', 'EQ<LA_NM-53002-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:29:50.685 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:30:32.368 [Rply]', 'EQ<LA_NM-53002-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:31:58.453 [Rply]', 'EQ<LA_NM-53002-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:34:15.609 [Rply]', 'EQ<LA_NM-53002-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:36:19.846 [Rply]', 'EQ<LA_NM-53002-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:37:17.512 [Rply]', 'EQ<LA_NM-53002-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:14:39.841 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:15:41.662 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:18:08.552 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:33:25.692 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:38:32.904 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 06:00:35.645 [Rply]', 'EQ<LA_NM-53002-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 06:01:10.670 [Rply]', 'EQ<LA_NM-53002-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 06:03:10.477 [Rply]', 'EQ<LA_NM-53002-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 06:03:15.617 [Rply]', 'EQ<LA_NM-53002-1> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 06:31:43.332 [Rply]', 'EQ<LA_NM-53002-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 06:31:48.457 [Rply]', 'EQ<LA_NM-53002-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 07:30:36.330 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 07:30:42.892 [Rply]', 'EQ<LA_NM-53002-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:05:46.448 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:09:15.207 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:55:14.443 [Rply]', 'EQ<LA_NM-53002-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:55:19.583 [Rply]', 'EQ<LA_NM-53002-3> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:56:53.370 [Rply]', 'EQ<LA_NM-53002-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:56:58.479 [Rply]', 'EQ<LA_NM-53002-2> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 09:30:15.781 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 09:35:22.513 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 10:15:01.669 [Rply]', 'EQ<LA_NM-53002-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 10:15:52.711 [Rply]', 'EQ<LA_NM-53002-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 10:17:03.328 [Rply]', 'EQ<LA_NM-53002-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 10:18:55.222 [Rply]', 'EQ<LA_NM-53002-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 10:19:02.940 [Rply]', 'EQ<LA_NM-53002-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 10:20:19.729 [Rply]', 'EQ<LA_NM-53002-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 10:25:13.573 [Rply]', 'EQ<LA_NM-53002-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 10:29:52.418 [Rply]', 'EQ<LA_NM-53002-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:07:04.289 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:07:53.175 [Rply]', 'EQ<LA_NM-53002-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:12:38.780 [Rply]', 'EQ<LA_NM-53002-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:12:43.857 [Rply]', 'EQ<LA_NM-53002-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:21:48.315 [Rply]', 'EQ<LA_NM-53002-1> Func<CheckLoader> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:22:09.391 [Rply]', 'EQ<LA_NM-53002-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:23:58.192 [Rply]', 'EQ<LA_NM-53002-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:24:29.579 [Rply]', 'EQ<LA_NM-53002-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:24:34.688 [Rply]', 'EQ<LA_NM-53002-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:33:14.843 [Rply]', 'EQ<LA_NM-53002-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:33:19.951 [Rply]', 'EQ<LA_NM-53002-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:33:24.998 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:33:30.013 [Rply]', 'EQ<LA_NM-53002-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:06:43.123 [Rply]', 'EQ<LA_NM-53002-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:06:48.248 [Rply]', 'EQ<LA_NM-53002-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:07:30.040 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:08:08.099 [Rply]', 'EQ<LA_NM-53002-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:12:22.805 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:14:03.294 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:21:32.834 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:28:03.380 [Rply]', 'EQ<LA_NM-53002-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:28:08.536 [Rply]', 'EQ<LA_NM-53002-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 13:43:53.714 [Rply]', 'EQ<LA_NM-53002-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 13:43:58.791 [Rply]', 'EQ<LA_NM-53002-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 13:44:08.556 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 13:45:44.921 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 13:47:22.832 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:07:33.478 [Rply]', 'EQ<LA_NM-53002-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:09:26.584 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:11:04.911 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:12:07.072 [Rply]', 'EQ<LA_NM-53002-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 21:35:03.881 [Rply]', 'EQ<LA_NM-53002-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理檔案: 100%|██████████████████████████████████████████████████████████████████████████| 6/6 [00:06<00:00,  1.08s/it]\n", "2025-06-23 11:15:06,355 - INFO - 合併處理結果...\n", "2025-06-23 11:15:06,363 - INFO - 服務 UploadData: 8295 筆記錄\n", "2025-06-23 11:15:06,365 - INFO - 服務 ChangeEQStatus: 2283 筆記錄\n", "2025-06-23 11:15:06,365 - INFO - 服務 CheckPanel: 2080 筆記錄\n", "2025-06-23 11:15:06,367 - INFO - 服務 UploadEventCode: 1360 筆記錄\n", "2025-06-23 11:15:06,369 - INFO - 服務 ChangeEQMode: 419 筆記錄\n", "2025-06-23 11:15:06,369 - INFO - 服務 CheckLoader: 109 筆記錄\n", "2025-06-23 11:15:06,369 - INFO - 服務 RemoveMLM: 69 筆記錄\n", "2025-06-23 11:15:06,369 - INFO - 服務 VerifyMLM: 58 筆記錄\n", "2025-06-23 11:15:06,369 - INFO - 服務 UploadRecipe: 11 筆記錄\n", "2025-06-23 11:15:06,369 - INFO - 服務 CheckTime: 51 筆記錄\n", "2025-06-23 11:15:06,369 - INFO - 寫入 Excel 檔案: \\\\k5dcnas02\\IME\\個人資料夾\\VICTOR\\對對對對對\\MO攔檢率\\Rowdata\\LA_NM-53002\\LA_NM-53002_25_W25.xlsx\n"]}, {"name": "stdout", "output_type": "stream", "text": ["解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 22:45:42.718 [Rply]', 'EQ<LA_NM-53002-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-23 11:15:15,230 - INFO - 完成處理資料夾: \\\\k5dcnas02\\IME\\AAS_LOG\\LA_NM-53002\n", "2025-06-23 11:15:15,363 - INFO - 開始處理週別 W25\n", "2025-06-23 11:15:15,363 - INFO - 找到 6 個檔案\n", "處理檔案:  50%|█████████████████████████████████████                                     | 3/6 [00:01<00:01,  1.99it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:39:51.775 [Rply]', 'EQ<LA_NM-53003-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:41:11.204 [Rply]', 'EQ<LA_NM-53003-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:42:36.319 [Rply]', 'EQ<LA_NM-53003-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:44:45.243 [Rply]', 'EQ<LA_NM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:47:41.396 [Rply]', 'EQ<LA_NM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:47:46.536 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:47:51.660 [Rply]', 'EQ<LA_NM-53003-4> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:47:58.878 [Rply]', 'EQ<LA_NM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 22:09:59.934 [Rply]', 'EQ<LA_NM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 22:10:05.011 [Rply]', 'EQ<LA_NM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 22:15:38.361 [Rply]', 'EQ<LA_NM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 22:16:28.871 [Rply]', 'EQ<LA_NM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 22:32:16.910 [Rply]', 'EQ<LA_NM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 22:37:25.283 [Rply]', 'EQ<LA_NM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 22:37:45.390 [Rply]', 'EQ<LA_NM-53003-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 22:57:34.559 [Rply]', 'EQ<LA_NM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 22:57:39.902 [Rply]', 'EQ<LA_NM-53003-4> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:02:33.354 [Rply]', 'EQ<LA_NM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:03:24.224 [Rply]', 'EQ<LA_NM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:50:00.808 [Rply]', 'EQ<LA_NM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:50:06.182 [Rply]', 'EQ<LA_NM-53003-1> Func<CheckLoader> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理檔案:  67%|█████████████████████████████████████████████████▎                        | 4/6 [00:01<00:00,  2.16it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:17:25.771 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:43:27.020 [Rply]', 'EQ<LA_NM-53003-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:04:23.150 [Rply]', 'EQ<LA_NM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:04:28.275 [Rply]', 'EQ<LA_NM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:04:40.711 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:05:00.092 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:06:13.006 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:57:43.480 [Rply]', 'EQ<LA_NM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:57:54.229 [Rply]', 'EQ<LA_NM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:00:52.897 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:18:10.926 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:18:16.316 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:18:27.580 [Rply]', 'EQ<LA_NM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:18:48.266 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:21:47.637 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:37:52.921 [Rply]', 'EQ<LA_NM-53003-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:37:58.108 [Rply]', 'EQ<LA_NM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:44:25.889 [Rply]', 'EQ<LA_NM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:44:36.341 [Rply]', 'EQ<LA_NM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:34:37.314 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:43:05.916 [Rply]', 'EQ<LA_NM-53003-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:44:21.861 [Rply]', 'EQ<LA_NM-53003-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:48:41.968 [Rply]', 'EQ<LA_NM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 05:48:47.108 [Rply]', 'EQ<LA_NM-53003-2> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 06:11:07.099 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 06:13:35.771 [Rply]', 'EQ<LA_NM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 06:13:56.441 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 06:54:26.751 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 07:21:10.625 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 07:21:19.890 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:03:00.475 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:10:08.179 [Rply]', 'EQ<LA_NM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:48:10.683 [Rply]', 'EQ<LA_NM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:48:23.838 [Rply]', 'EQ<LA_NM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:48:29.446 [Rply]', 'EQ<LA_NM-53003-1> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:48:34.915 [Rply]', 'EQ<LA_NM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:48:40.055 [Rply]', 'EQ<LA_NM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:48:45.195 [Rply]', 'EQ<LA_NM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 09:07:56.271 [Rply]', 'EQ<LA_NM-53003-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 09:09:05.654 [Rply]', 'EQ<LA_NM-53003-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 09:09:11.856 [Rply]', 'EQ<LA_NM-53003-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 09:10:29.848 [Rply]', 'EQ<LA_NM-53003-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 09:11:30.195 [Rply]', 'EQ<LA_NM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 09:11:58.757 [Rply]', 'EQ<LA_NM-53003-3> Func<UploadRecipe> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 09:13:59.900 [Rply]', 'EQ<LA_NM-53003-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 09:50:36.768 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 09:50:43.408 [Rply]', 'EQ<LA_NM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 10:08:32.396 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 10:08:39.021 [Rply]', 'EQ<LA_NM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 10:08:44.098 [Rply]', 'EQ<LA_NM-53003-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 10:08:58.019 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 10:14:30.498 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 10:14:37.138 [Rply]', 'EQ<LA_NM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:16:53.043 [Rply]', 'EQ<LA_NM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:23:23.360 [Rply]', 'EQ<LA_NM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:23:42.999 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:23:48.029 [Rply]', 'EQ<LA_NM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:23:53.154 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:24:18.901 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:24:53.366 [Rply]', 'EQ<LA_NM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:25:12.520 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:25:22.800 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:25:49.048 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:33:16.479 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理檔案:  83%|█████████████████████████████████████████████████████████████▋            | 5/6 [00:03<00:00,  1.20it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:37:19.641 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:59:53.885 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:00:14.992 [Rply]', 'EQ<LA_NM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:00:20.539 [Rply]', 'EQ<LA_NM-53003-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:04:49.072 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:05:22.522 [Rply]', 'EQ<LA_NM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:46:04.706 [Rply]', 'EQ<LA_NM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:53:12.114 [Rply]', 'EQ<LA_NM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 13:11:56.336 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 13:15:09.784 [Rply]', 'EQ<LA_NM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 13:15:15.018 [Rply]', 'EQ<LA_NM-53003-2> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 13:19:09.696 [Rply]', 'EQ<LA_NM-53003-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 13:19:14.883 [Rply]', 'EQ<LA_NM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 13:21:31.883 [Rply]', 'EQ<LA_NM-53003-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 13:31:44.453 [Rply]', 'EQ<LA_NM-53003-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 13:39:10.092 [Rply]', 'EQ<LA_NM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 13:39:15.217 [Rply]', 'EQ<LA_NM-53003-1> Func<CheckLoader> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:11:24.582 [Rply]', 'EQ<LA_NM-53003-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:26:16.298 [Rply]', 'EQ<LA_NM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:26:21.376 [Rply]', 'EQ<LA_NM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:26:26.453 [Rply]', 'EQ<LA_NM-53003-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:26:31.500 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:45:30.512 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:45:37.137 [Rply]', 'EQ<LA_NM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:50:59.414 [Rply]', 'EQ<LA_NM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:12:30.458 [Rply]', 'EQ<LA_NM-53003-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:13:01.924 [Rply]', 'EQ<LA_NM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:16:45.962 [Rply]', 'EQ<LA_NM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:16:51.118 [Rply]', 'EQ<LA_NM-53003-1> Func<CheckLoader> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:16:56.523 [Rply]', 'EQ<LA_NM-53003-1> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:17:01.601 [Rply]', 'EQ<LA_NM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:17:06.710 [Rply]', 'EQ<LA_NM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:17:11.819 [Rply]', 'EQ<LA_NM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:17:16.959 [Rply]', 'EQ<LA_NM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:17:22.208 [Rply]', 'EQ<LA_NM-53003-1> Func<CheckLoader> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:17:27.317 [Rply]', 'EQ<LA_NM-53003-1> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:17:32.441 [Rply]', 'EQ<LA_NM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:17:48.518 [Rply]', 'EQ<LA_NM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:17:53.627 [Rply]', 'EQ<LA_NM-53003-1> Func<CheckLoader> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:17:58.767 [Rply]', 'EQ<LA_NM-53003-1> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:18:03.907 [Rply]', 'EQ<LA_NM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:19:52.645 [Rply]', 'EQ<LA_NM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:19:57.785 [Rply]', 'EQ<LA_NM-53003-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:20:03.191 [Rply]', 'EQ<LA_NM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:20:11.268 [Rply]', 'EQ<LA_NM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:20:16.361 [Rply]', 'EQ<LA_NM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:20:21.439 [Rply]', 'EQ<LA_NM-53003-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:20:26.563 [Rply]', 'EQ<LA_NM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:20:31.828 [Rply]', 'EQ<LA_NM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:20:36.968 [Rply]', 'EQ<LA_NM-53003-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:21:18.261 [Rply]', 'EQ<LA_NM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:21:40.102 [Rply]', 'EQ<LA_NM-53003-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:21:45.352 [Rply]', 'EQ<LA_NM-53003-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:21:50.460 [Rply]', 'EQ<LA_NM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:21:55.522 [Rply]', 'EQ<LA_NM-53003-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:22:34.721 [Rply]', 'EQ<LA_NM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:22:48.392 [Rply]', 'EQ<LA_NM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:22:53.516 [Rply]', 'EQ<LA_NM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:22:58.641 [Rply]', 'EQ<LA_NM-53003-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:23:04.109 [Rply]', 'EQ<LA_NM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:23:52.338 [Rply]', 'EQ<LA_NM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:23:57.415 [Rply]', 'EQ<LA_NM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:24:02.837 [Rply]', 'EQ<LA_NM-53003-1> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:24:08.071 [Rply]', 'EQ<LA_NM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:24:13.211 [Rply]', 'EQ<LA_NM-53003-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:24:18.429 [Rply]', 'EQ<LA_NM-53003-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:24:23.475 [Rply]', 'EQ<LA_NM-53003-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:25:29.702 [Rply]', 'EQ<LA_NM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:25:34.796 [Rply]', 'EQ<LA_NM-53003-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:26:22.228 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:26:27.290 [Rply]', 'EQ<LA_NM-53003-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:26:49.147 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:26:59.427 [Rply]', 'EQ<LA_NM-53003-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:27:42.188 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:27:55.468 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:28:21.184 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:29:20.974 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:29:38.972 [Rply]', 'EQ<LA_NM-53003-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:30:20.390 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:31:22.008 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:31:32.320 [Rply]', 'EQ<LA_NM-53003-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:31:52.520 [Rply]', 'EQ<LA_NM-53003-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:32:13.143 [Rply]', 'EQ<LA_NM-53003-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 16:44:45.280 [Rply]', 'EQ<LA_NM-53003-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 16:45:04.403 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 16:46:34.065 [Rply]', 'EQ<LA_NM-53003-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 22:06:12.539 [Rply]', 'EQ<LA_NM-53003-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理檔案: 100%|██████████████████████████████████████████████████████████████████████████| 6/6 [00:03<00:00,  1.51it/s]\n", "2025-06-23 11:15:19,368 - INFO - 合併處理結果...\n", "2025-06-23 11:15:19,396 - INFO - 服務 ChangeEQStatus: 2739 筆記錄\n"]}, {"name": "stdout", "output_type": "stream", "text": ["解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/21 18:25:20.389 [Rply]', 'EQ<LA_NM-53003-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-23 11:15:19,397 - INFO - 服務 ChangeEQMode: 470 筆記錄\n", "2025-06-23 11:15:19,398 - INFO - 服務 UploadEventCode: 5954 筆記錄\n", "2025-06-23 11:15:19,398 - INFO - 服務 RemoveMLM: 102 筆記錄\n", "2025-06-23 11:15:19,398 - INFO - 服務 VerifyMLM: 71 筆記錄\n", "2025-06-23 11:15:19,398 - INFO - 服務 CheckLoader: 125 筆記錄\n", "2025-06-23 11:15:19,398 - INFO - 服務 CheckPanel: 2566 筆記錄\n", "2025-06-23 11:15:19,398 - INFO - 服務 UploadData: 10196 筆記錄\n", "2025-06-23 11:15:19,398 - INFO - 服務 CheckTime: 31 筆記錄\n", "2025-06-23 11:15:19,398 - INFO - 服務 UploadRecipe: 14 筆記錄\n", "2025-06-23 11:15:19,398 - INFO - 寫入 Excel 檔案: \\\\k5dcnas02\\IME\\個人資料夾\\VICTOR\\對對對對對\\MO攔檢率\\Rowdata\\LA_NM-53003\\LA_NM-53003_25_W25.xlsx\n", "2025-06-23 11:15:32,808 - INFO - 完成處理資料夾: \\\\k5dcnas02\\IME\\AAS_LOG\\LA_NM-53003\n", "2025-06-23 11:15:32,959 - INFO - 開始處理週別 W25\n", "2025-06-23 11:15:32,959 - INFO - 找到 6 個檔案\n", "處理檔案:  33%|████████████████████████▋                                                 | 2/6 [00:00<00:01,  3.19it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 18:56:28.342 [Rply]', 'EQ<LA_NM-53004-3> Func<UploadRecipe> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 19:51:21.791 [Rply]', 'EQ<LA_NM-53004-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:31:34.267 [Rply]', 'EQ<LA_NM-53004-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 21:31:39.344 [Rply]', 'EQ<LA_NM-53004-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/19 23:18:58.264 [Rply]', 'EQ<LA_NM-53004-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理檔案:  50%|█████████████████████████████████████                                     | 3/6 [00:01<00:01,  2.41it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:03:39.190 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 00:31:49.618 [Rply]', 'EQ<LA_NM-53004-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:21:00.687 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:22:17.444 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:23:12.563 [Rply]', 'EQ<LA_NM-53004-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:23:21.781 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:45:58.870 [Rply]', 'EQ<LA_NM-53004-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:46:03.994 [Rply]', 'EQ<LA_NM-53004-3> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 01:46:16.306 [Rply]', 'EQ<LA_NM-53004-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:09:59.237 [Rply]', 'EQ<LA_NM-53004-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:10:04.455 [Rply]', 'EQ<LA_NM-53004-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:10:09.564 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:10:15.204 [Rply]', 'EQ<LA_NM-53004-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:10:32.077 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:20:23.983 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 02:20:32.201 [Rply]', 'EQ<LA_NM-53004-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:00:02.748 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:00:10.435 [Rply]', 'EQ<LA_NM-53004-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:00:27.511 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:14:13.954 [Rply]', 'EQ<LA_NM-53004-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:15:02.949 [Rply]', 'EQ<LA_NM-53004-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:20:03.635 [Rply]', 'EQ<LA_NM-53004-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:20:22.212 [Rply]', 'EQ<LA_NM-53004-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:20:27.289 [Rply]', 'EQ<LA_NM-53004-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 03:22:58.804 [Rply]', 'EQ<LA_NM-53004-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:05:39.196 [Rply]', 'EQ<LA_NM-53004-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:05:58.022 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:48:42.826 [Rply]', 'EQ<LA_NM-53004-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 04:57:55.626 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 06:37:17.133 [Rply]', 'EQ<LA_NM-53004-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 06:37:35.490 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 06:39:01.575 [Rply]', 'EQ<LA_NM-53004-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 06:39:06.699 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 06:55:48.274 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 06:59:36.687 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 07:00:13.121 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 07:00:44.992 [Rply]', 'EQ<LA_NM-53004-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 07:01:04.881 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 07:01:14.239 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 08:47:29.451 [Rply]', 'EQ<LA_NM-53004-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 09:08:32.660 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 09:08:40.675 [Rply]', 'EQ<LA_NM-53004-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 09:37:55.996 [Rply]', 'EQ<LA_NM-53004-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 09:38:01.183 [Rply]', 'EQ<LA_NM-53004-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 09:44:08.253 [Rply]', 'EQ<LA_NM-53004-2> Func<UploadRecipe> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 09:44:13.347 [Rply]', 'EQ<LA_NM-53004-3> Func<UploadRecipe> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 09:44:22.814 [Rply]', 'EQ<LA_NM-53004-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:20:28.085 [Rply]', 'EQ<LA_NM-53004-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:20:33.210 [Rply]', 'EQ<LA_NM-53004-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:20:38.350 [Rply]', 'EQ<LA_NM-53004-1> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:20:48.817 [Rply]', 'EQ<LA_NM-53004-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:33:05.390 [Rply]', 'EQ<LA_NM-53004-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:33:10.515 [Rply]', 'EQ<LA_NM-53004-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:33:20.857 [Rply]', 'EQ<LA_NM-53004-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:58:23.394 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:58:31.612 [Rply]', 'EQ<LA_NM-53004-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:58:40.330 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 11:58:45.439 [Rply]', 'EQ<LA_NM-53004-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:01:22.437 [Rply]', 'EQ<LA_NM-53004-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:02:29.102 [Rply]', 'EQ<LA_NM-53004-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:02:42.273 [Rply]', 'EQ<LA_NM-53004-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:21:22.153 [Rply]', 'EQ<LA_NM-53004-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:21:31.480 [Rply]', 'EQ<LA_NM-53004-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:49:39.590 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 12:49:44.606 [Rply]', 'EQ<LA_NM-53004-1> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理檔案:  83%|█████████████████████████████████████████████████████████████▋            | 5/6 [00:02<00:00,  1.64it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 13:52:21.557 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 13:52:48.476 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:24:05.977 [Rply]', 'EQ<LA_NM-53004-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:24:11.101 [Rply]', 'EQ<LA_NM-53004-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:24:21.444 [Rply]', 'EQ<LA_NM-53004-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:24:38.442 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:25:03.112 [Rply]', 'EQ<LA_NM-53004-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:26:21.463 [Rply]', 'EQ<LA_NM-53004-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:26:42.523 [Rply]', 'EQ<LA_NM-53004-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:26:47.663 [Rply]', 'EQ<LA_NM-53004-1> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:36:23.570 [Rply]', 'EQ<LA_NM-53004-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:36:28.663 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:36:33.756 [Rply]', 'EQ<LA_NM-53004-2> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:37:43.389 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:41:03.899 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:41:58.513 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:49:35.745 [Rply]', 'EQ<LA_NM-53004-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:49:40.885 [Rply]', 'EQ<LA_NM-53004-2> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:51:14.141 [Rply]', 'EQ<LA_NM-53004-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 14:51:19.250 [Rply]', 'EQ<LA_NM-53004-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:10:02.979 [Rply]', 'EQ<LA_NM-53004-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:15:17.008 [Rply]', 'EQ<LA_NM-53004-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:15:43.317 [Rply]', 'EQ<LA_NM-53004-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:15:48.395 [Rply]', 'EQ<LA_NM-53004-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:16:00.425 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:17:17.026 [Rply]', 'EQ<LA_NM-53004-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:17:22.088 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:17:27.525 [Rply]', 'EQ<LA_NM-53004-2> Func<ChangeEQMode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:18:20.613 [Rply]', 'EQ<LA_NM-53004-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:18:26.143 [Rply]', 'EQ<LA_NM-53004-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:18:37.705 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:19:56.274 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:20:41.598 [Rply]', 'EQ<LA_NM-53004-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:20:47.128 [Rply]', 'EQ<LA_NM-53004-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:21:12.907 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:21:29.296 [Rply]', 'EQ<LA_NM-53004-3> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:22:01.245 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:22:21.259 [Rply]', 'EQ<LA_NM-53004-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:22:53.584 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:23:01.239 [Rply]', 'EQ<LA_NM-53004-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:24:47.446 [Rply]', 'EQ<LA_NM-53004-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:27:13.150 [Rply]', 'EQ<LA_NM-53004-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:27:23.992 [Rply]', 'EQ<LA_NM-53004-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:28:52.764 [Rply]', 'EQ<LA_NM-53004-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:29:02.200 [Rply]', 'EQ<LA_NM-53004-2> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:30:30.488 [Rply]', 'EQ<LA_NM-53004-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:52:43.188 [Rply]', 'EQ<LA_NM-53004-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:52:48.297 [Rply]', 'EQ<LA_NM-53004-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:53:47.478 [Rply]', 'EQ<LA_NM-53004-1> Func<CheckPanel> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:53:52.572 [Rply]', 'EQ<LA_NM-53004-3> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:54:30.286 [Rply]', 'EQ<LA_NM-53004-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:54:45.207 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadEventCode> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:55:11.313 [Rply]', 'EQ<LA_NM-53004-4> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:55:21.578 [Rply]', 'EQ<LA_NM-53004-4> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 15:55:35.014 [Rply]', 'EQ<LA_NM-53004-2> Func<UploadData> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 16:31:03.436 [Rply]', 'EQ<LA_NM-53004-2> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/20 16:34:55.880 [Rply]', 'EQ<LA_NM-53004-1> Func<ChangeEQStatus> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/21 03:56:43.512 [Rply]', 'EQ<LA_NM-53004-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/21 05:30:26.443 [Rply]', 'EQ<LA_NM-53004-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/21 06:12:04.779 [Rply]', 'EQ<LA_NM-53004-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/21 10:04:11.798 [Rply]', 'EQ<LA_NM-53004-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/21 14:07:14.662 [Rply]', 'EQ<LA_NM-53004-2> Func<RemoveMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n", "解析錯誤: Expecting value: line 1 column 1 (char 0)\n", "問題資料: ['2025/06/21 17:45:36.426 [Rply]', 'EQ<LA_NM-53004-2> Func<VerifyMLM> [Alarm] Exception! [SendRequest]Wait Reply Timeout: 5000ms']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理檔案: 100%|██████████████████████████████████████████████████████████████████████████| 6/6 [00:03<00:00,  1.70it/s]\n", "2025-06-23 11:15:36,515 - INFO - 合併處理結果...\n", "2025-06-23 11:15:36,523 - INFO - 服務 ChangeEQStatus: 2902 筆記錄\n", "2025-06-23 11:15:36,523 - INFO - 服務 ChangeEQMode: 835 筆記錄\n", "2025-06-23 11:15:36,523 - INFO - 服務 CheckLoader: 273 筆記錄\n", "2025-06-23 11:15:36,523 - INFO - 服務 CheckPanel: 2378 筆記錄\n", "2025-06-23 11:15:36,523 - INFO - 服務 UploadData: 9434 筆記錄\n", "2025-06-23 11:15:36,523 - INFO - 服務 UploadEventCode: 2541 筆記錄\n", "2025-06-23 11:15:36,523 - INFO - 服務 RemoveMLM: 89 筆記錄\n", "2025-06-23 11:15:36,523 - INFO - 服務 VerifyMLM: 64 筆記錄\n", "2025-06-23 11:15:36,523 - INFO - 服務 CheckTime: 35 筆記錄\n", "2025-06-23 11:15:36,523 - INFO - 服務 UploadRecipe: 20 筆記錄\n", "2025-06-23 11:15:36,523 - INFO - 寫入 Excel 檔案: \\\\k5dcnas02\\IME\\個人資料夾\\VICTOR\\對對對對對\\MO攔檢率\\Rowdata\\LA_NM-53004\\LA_NM-53004_25_W25.xlsx\n", "2025-06-23 11:15:47,126 - INFO - 完成處理資料夾: \\\\k5dcnas02\\IME\\AAS_LOG\\LA_NM-53004\n", "2025-06-23 11:15:47,259 - INFO - 開始處理週別 W25\n", "2025-06-23 11:15:47,259 - INFO - 找到 6 個檔案\n", "處理檔案: 100%|██████████████████████████████████████████████████████████████████████████| 6/6 [00:09<00:00,  1.66s/it]\n", "2025-06-23 11:15:57,243 - INFO - 合併處理結果...\n", "2025-06-23 11:15:57,275 - INFO - 服務 ChangeEQStatus: 1712 筆記錄\n", "2025-06-23 11:15:57,276 - INFO - 服務 UploadRecipe: 46 筆記錄\n", "2025-06-23 11:15:57,278 - INFO - 服務 ChangeEQMode: 301 筆記錄\n", "2025-06-23 11:15:57,279 - INFO - 服務 UploadEventCode: 4916 筆記錄\n", "2025-06-23 11:15:57,281 - INFO - 服務 CheckLoader: 288 筆記錄\n", "2025-06-23 11:15:57,281 - INFO - 服務 CheckPanel: 9841 筆記錄\n", "2025-06-23 11:15:57,283 - INFO - 服務 UploadData: 29454 筆記錄\n", "2025-06-23 11:15:57,283 - INFO - 服務 CheckTime: 31 筆記錄\n", "2025-06-23 11:15:57,286 - INFO - 寫入 Excel 檔案: \\\\k5dcnas02\\IME\\個人資料夾\\VICTOR\\對對對對對\\MO攔檢率\\Rowdata\\MEC_FK-53101\\MEC_FK-53101_25_W25.xlsx\n", "2025-06-23 11:16:22,259 - INFO - 完成處理資料夾: \\\\k5dcnas02\\IME\\AAS_LOG\\MEC_FK-53101\n", "2025-06-23 11:16:22,423 - INFO - 開始處理週別 W25\n", "2025-06-23 11:16:22,424 - INFO - 找到 6 個檔案\n", "處理檔案: 100%|██████████████████████████████████████████████████████████████████████████| 6/6 [00:02<00:00,  2.30it/s]\n", "2025-06-23 11:16:25,039 - INFO - 合併處理結果...\n", "2025-06-23 11:16:25,060 - INFO - 服務 UploadEventCode: 3155 筆記錄\n", "2025-06-23 11:16:25,061 - INFO - 服務 CheckLoader: 115 筆記錄\n", "2025-06-23 11:16:25,063 - INFO - 服務 ChangeEQStatus: 642 筆記錄\n", "2025-06-23 11:16:25,064 - INFO - 服務 ChangeEQMode: 268 筆記錄\n", "2025-06-23 11:16:25,065 - INFO - 服務 CheckPanel: 2358 筆記錄\n", "2025-06-23 11:16:25,066 - INFO - 服務 UploadData: 6899 筆記錄\n", "2025-06-23 11:16:25,067 - INFO - 服務 CheckTime: 30 筆記錄\n", "2025-06-23 11:16:25,067 - INFO - 服務 UploadRecipe: 20 筆記錄\n", "2025-06-23 11:16:25,068 - INFO - 寫入 Excel 檔案: \\\\k5dcnas02\\IME\\個人資料夾\\VICTOR\\對對對對對\\MO攔檢率\\Rowdata\\MEC_FK-53301\\MEC_FK-53301_25_W25.xlsx\n", "2025-06-23 11:16:33,918 - INFO - 完成處理資料夾: \\\\k5dcnas02\\IME\\AAS_LOG\\MEC_FK-53301\n", "2025-06-23 11:16:34,044 - INFO - 開始處理週別 W25\n", "2025-06-23 11:16:34,051 - INFO - 找到 6 個檔案\n", "處理檔案: 100%|██████████████████████████████████████████████████████████████████████████| 6/6 [00:03<00:00,  1.69it/s]\n", "2025-06-23 11:16:37,612 - INFO - 合併處理結果...\n", "2025-06-23 11:16:37,623 - INFO - 服務 UploadData: 10391 筆記錄\n", "2025-06-23 11:16:37,625 - INFO - 服務 ChangeEQStatus: 5436 筆記錄\n", "2025-06-23 11:16:37,625 - INFO - 服務 CheckPanel: 2288 筆記錄\n", "2025-06-23 11:16:37,626 - INFO - 服務 UploadEventCode: 1645 筆記錄\n", "2025-06-23 11:16:37,628 - INFO - 服務 ChangeEQMode: 356 筆記錄\n", "2025-06-23 11:16:37,629 - INFO - 服務 CheckLoader: 100 筆記錄\n", "2025-06-23 11:16:37,631 - INFO - 服務 UploadRecipe: 57 筆記錄\n", "2025-06-23 11:16:37,631 - INFO - 服務 RemoveMLM: 49 筆記錄\n", "2025-06-23 11:16:37,633 - INFO - 服務 VerifyMLM: 31 筆記錄\n", "2025-06-23 11:16:37,633 - INFO - 服務 CheckTime: 39 筆記錄\n", "2025-06-23 11:16:37,635 - INFO - 寫入 Excel 檔案: \\\\k5dcnas02\\IME\\個人資料夾\\VICTOR\\對對對對對\\MO攔檢率\\Rowdata\\SMVC_RH600-53002\\SMVC_RH600-53002_25_W25.xlsx\n", "2025-06-23 11:16:49,460 - INFO - 完成處理資料夾: \\\\k5dcnas02\\IME\\AAS_LOG\\SMVC_RH600-53002\n"]}], "source": ["def get_week_number(date_str):\n", "    \"\"\"根據日期取得週別\"\"\"\n", "    date = datetime.strptime(date_str, '%Y%m%d')\n", "    base_date = datetime.strptime('20250202', '%Y%m%d')  # W6 開始日期\n", "    days_diff = (date - base_date).days\n", "    week_diff = days_diff // 7\n", "    return week_diff + 6  # 從 W6 開始\n", "\n", "def get_date_range_for_week(week):\n", "    \"\"\"取得指定週別的日期範圍\"\"\"\n", "    base_date = datetime.strptime('20250202', '%Y%m%d')  # W6 開始日期\n", "    days_to_add = (week - 6) * 7  # 從 W6 開始計算\n", "    start_date = base_date + timedelta(days=days_to_add)\n", "    end_date = start_date + <PERSON><PERSON><PERSON>(days=6)\n", "    return start_date, end_date\n", "import json\n", "from datetime import datetime\n", "import pandas as pd\n", "import re\n", "\n", "def setup_logging():\n", "    \"\"\"設定日誌\"\"\"\n", "    logging.basicConfig(\n", "        level=logging.INFO,\n", "        format='%(asctime)s - %(levelname)s - %(message)s',\n", "        handlers=[\n", "            logging.FileHandler('log_processor.log'),\n", "            logging.StreamHandler()\n", "        ]\n", "    )\n", "\n", "def process_single_file_excel(file_path: Path) -> Dict[str, List[Dict[str, Any]]]:\n", "    \"\"\"處理單個檔案\"\"\"\n", "    try:\n", "        entries = parse_log_file(str(file_path))\n", "        if not entries:\n", "            logging.warning(f\"檔案無有效記錄: {file_path}\")\n", "            return {}\n", "        \n", "        return process_entries(entries)\n", "    except Exception as e:\n", "        logging.error(f\"處理檔案時發生錯誤 {file_path}: {str(e)}\")\n", "        return {}\n", "\n", "def merge_results(results: List[Dict[str, List[Dict[str, Any]]]]) -> Dict[str, List[Dict[str, Any]]]:\n", "    \"\"\"合併處理結果\"\"\"\n", "    merged = {}\n", "    for result in results:\n", "        for service_name, records in result.items():\n", "            if service_name not in merged:\n", "                merged[service_name] = []\n", "            merged[service_name].extend(records)\n", "    \n", "    # 排序每個服務的記錄\n", "    for service_name in merged:\n", "        merged[service_name].sort(key=lambda x: x['Rqst時間'])\n", "    \n", "    return merged\n", "\n", "def process_folder(folder_path: str, week: int, max_workers: int = 4) -> None:\n", "    \"\"\"處理資料夾中的所有檔案\"\"\"\n", "    try:\n", "        # 設定路徑\n", "        folder = Path(folder_path)\n", "\n", "        output_folder = Path(r\"\\\\k5dcnas02\\IME\\個人資料夾\\VICTOR\\對對對對對\\MO攔檢率\\Rowdata\")/folder.name  # 在當前路徑建立 byweek 資料夾\n", "        output_folder.mkdir(parents=True, exist_ok=True)\n", "        \n", "        start_date, _ = get_date_range_for_week(week)\n", "        year_suffix = start_date.strftime('%y')\n", "        output_file = output_folder / (folder.name + f\"_{year_suffix}_W{week}.xlsx\")\n", "        \n", "        # 取得週別的日期範圍\n", "        start_date, end_date = get_date_range_for_week(week)\n", "        \n", "        # 獲取所有相關月份的檔案\n", "        all_files = []\n", "        months_to_check = set()\n", "        current_date = start_date\n", "        while current_date <= end_date:\n", "            months_to_check.add(current_date.strftime('%Y%m'))\n", "            current_date += <PERSON><PERSON><PERSON>(days=1)\n", "        \n", "        # 收集所有相關月份的檔案\n", "        for month in months_to_check:\n", "            month_folder = folder / month  # 修改：直接在folder下找月份資料夾\n", "            if month_folder.exists():\n", "                all_files.extend(list(month_folder.glob('*.log')))\n", "        \n", "        if not all_files:\n", "            logging.warning(f\"找不到週別 W{week} 的相關檔案\")\n", "            return\n", "        \n", "        # 過濾出該週的檔案\n", "        files = []\n", "        for f in all_files:\n", "            try:\n", "                # 從檔案名稱中提取日期\n", "                file_date_str = f.stem.split('_')[-1]\n", "                file_date = datetime.strptime(file_date_str, '%Y%m%d')\n", "                if start_date <= file_date <= end_date:\n", "                    files.append(f)\n", "            except (ValueError, IndexError) as e:\n", "                logging.warning(f\"無法解析檔案日期: {f.name}, 錯誤: {e}\")\n", "                continue\n", "        \n", "        if not files:\n", "            logging.warning(f\"週別 W{week} 沒有符合的檔案\")\n", "            return\n", "        \n", "        logging.info(f\"開始處理週別 W{week}\")\n", "        logging.info(f\"找到 {len(files)} 個檔案\")\n", "        \n", "        # 使用線程池處理檔案\n", "        results = []\n", "        with ThreadPoolExecutor(max_workers=max_workers) as executor:\n", "            # 建立任務\n", "            future_to_file = {\n", "                executor.submit(process_single_file_excel, file): file \n", "                for file in files\n", "            }\n", "            \n", "            # 使用 tqdm 顯示進度\n", "            with tqdm(total=len(files), desc=\"處理檔案\") as pbar:\n", "                for future in as_completed(future_to_file):\n", "                    file = future_to_file[future]\n", "                    try:\n", "                        result = future.result()\n", "                        if result:\n", "                            results.append(result)\n", "                    except Exception as e:\n", "                        logging.error(f\"處理檔案失敗 {file}: {str(e)}\")\n", "                    pbar.update(1)\n", "        \n", "        # 合併結果\n", "        if not results:\n", "            logging.warning(f\"沒有可用的處理結果: {folder_path}\")\n", "            return\n", "            \n", "        logging.info(\"合併處理結果...\")\n", "        merged_data = merge_results(results)\n", "        \n", "        # 顯示統計資訊\n", "        for service_name, records in merged_data.items():\n", "            logging.info(f\"服務 {service_name}: {len(records)} 筆記錄\")\n", "        \n", "        # 寫入 Excel\n", "        logging.info(f\"寫入 Excel 檔案: {output_file}\")\n", "        create_excel(merged_data, output_file)\n", "        \n", "        logging.info(f\"完成處理資料夾: {folder_path}\")\n", "        \n", "    except Exception as e:\n", "        logging.error(f\"處理資料夾時發生錯誤 {folder_path}: {str(e)}\")\n", "\n", "\n", "def parse_log_entry(lines):\n", "    \"\"\"解析單個日誌條目\"\"\"\n", "    try:\n", "        # 解析時間戳和類型\n", "        first_line = lines[0].strip()\n", "        timestamp_match = re.match(r'(\\d{4}/\\d{2}/\\d{2}\\s+\\d{2}:\\d{2}:\\d{2}\\.\\d{3})', first_line)\n", "        if not timestamp_match:\n", "            return None\n", "        \n", "        timestamp = datetime.strptime(timestamp_match.group(1), '%Y/%m/%d %H:%M:%S.%f')\n", "        \n", "        # 組合並解析 JSON\n", "        json_text = ''.join(lines[1:])\n", "        data = json.loads(json_text)\n", "        \n", "        return {\n", "            'timestamp': timestamp,\n", "            'data': data,\n", "            'type': 'request' if '[Rqst]' in first_line else 'reply'\n", "        }\n", "    except Exception as e:\n", "        print(f\"解析錯誤: {str(e)}\")\n", "        print(f\"問題資料: {lines}\")\n", "        return None\n", "\n", "def parse_log_file(file_path):\n", "    \"\"\"解析整個日誌檔案\"\"\"\n", "    entries = []\n", "    current_entry = []\n", "    \n", "    try:\n", "        # 一次性讀取整個文件內容\n", "        with open(file_path, 'r', encoding='utf-8-sig') as file:\n", "            lines = file.readlines()\n", "            \n", "        for line in lines:\n", "            line = line.rstrip()\n", "            \n", "            # 檢查是否為新條目的開始\n", "            if re.match(r'\\d{4}/\\d{2}/\\d{2}', line):\n", "                if current_entry:\n", "                    parsed = parse_log_entry(current_entry)\n", "                    if parsed:\n", "                        entries.append(parsed)\n", "                current_entry = [line]\n", "            else:\n", "                if line:  # 只添加非空行\n", "                    current_entry.append(line)\n", "        \n", "        # 處理最後一個條目\n", "        if current_entry:\n", "            parsed = parse_log_entry(current_entry)\n", "            if parsed:\n", "                entries.append(parsed)\n", "    \n", "    except Exception as e:\n", "        logging.error(f\"檔案讀取錯誤 {file_path}: {str(e)}\")\n", "        return []\n", "        \n", "    return entries\n", "\n", "def pair_requests_replies(entries):\n", "    \"\"\"配對請求和回覆\n", "    \n", "    配對規則：\n", "    1. 回覆必須在請求之後\n", "    2. 如果一個請求後立即跟著一個回覆，則視為配對\n", "    3. 如果一個請求後面是另一個請求，則第一個請求視為無回覆\n", "    4. 一個回覆只能配對一個請求\n", "    \"\"\"\n", "    pairs = []\n", "    \n", "    # 為了更容易處理，先把條目按時間排序\n", "    entries = sorted(entries, key=lambda x: x['timestamp'])\n", "    \n", "    # 找出所有請求的索引\n", "    request_indices = [i for i, e in enumerate(entries) if e['type'] == 'request']\n", "    \n", "    for i, req_idx in enumerate(request_indices):\n", "        request = entries[req_idx]\n", "        matching_reply = None\n", "        \n", "        # 檢查下一個條目\n", "        if req_idx + 1 < len(entries):\n", "            next_entry = entries[req_idx + 1]\n", "            \n", "            # 如果下一個條目是回覆，則配對\n", "            if next_entry['type'] == 'reply' and next_entry['data']['service_name'] == request['data']['service_name']:\n", "                matching_reply = next_entry\n", "            # 如果下一個條目是請求，則當前請求視為無回覆\n", "            elif next_entry['type'] == 'request':\n", "                matching_reply = None\n", "        \n", "        # 建立配對記錄\n", "        pairs.append({\n", "            'request': request,\n", "            'reply': matching_reply\n", "        })\n", "        \n", "        # 打印配對結果（用於調試）\n", "        # print(f\"\\n配對結果:\")\n", "        # print(f\"Rqst時間: {request['timestamp']}\")\n", "        # print(f\"請求類型: {request['data']['service_name']}\")\n", "        # if matching_reply:\n", "        #     print(f\"Rply時間: {matching_reply['timestamp']}\")\n", "        #     print(f\"回覆類型: {matching_reply['data']['service_name']}\")\n", "        # else:\n", "        #     print(\"無匹配回覆\")\n", "    \n", "    return pairs\n", "\n", "def process_b_field(b_data, record, prefix='Rqst_'):\n", "    \"\"\"處理 B 欄位中的數據，包括嵌套的字典和陣列\n", "    \n", "    Args:\n", "        b_data: B 欄位的數據\n", "        record: 要更新的記錄字典\n", "        prefix: 欄位名稱的前綴\n", "    \"\"\"\n", "    for key, value in b_data.items():\n", "        if isinstance(value, list) and value and isinstance(value[0], dict):\n", "            # 處理字典陣列\n", "            for i, item in enumerate(value):\n", "                for k, v in item.items():\n", "                    # 使用格式化的欄位名稱：prefix_原始key_陣列索引_字典key\n", "                    column_name = f\"{prefix}{key}_{i+1}_{k}\"\n", "                    record[column_name] = v\n", "        else:\n", "            # 處理一般值\n", "            record[f\"{prefix}{key}\"] = value\n", "\n", "def process_entries(entries):\n", "    \"\"\"處理解析後的條目\"\"\"\n", "    # 配對請求和回覆\n", "    pairs = pair_requests_replies(entries)\n", "    \n", "    # 按 A 類型分組\n", "    grouped_data = {}\n", "    \n", "    for pair in pairs:\n", "        request = pair['request']\n", "        reply = pair['reply']\n", "        \n", "        # 使用 A 作為分組依據\n", "        a_type = request['data']['service_name']\n", "        \n", "        if a_type not in grouped_data:\n", "            grouped_data[a_type] = []\n", "            \n", "        record = {\n", "            'Rqst時間': request['timestamp'],\n", "            'Rply時間': reply['timestamp'] if reply else '無回復資料',\n", "            '處理時間(秒)': (reply['timestamp'] - request['timestamp']).total_seconds() if reply else None,\n", "            'service_name': a_type\n", "        }\n", "        \n", "        # 處理請求資料 (request_data)\n", "        request_data = request['data'].get('request_data')\n", "        if isinstance(request_data, dict):\n", "            for key, value in request_data.items():\n", "                record[f'request_data_{key}'] = value\n", "        elif request_data is not None:\n", "            record['request_data'] = request_data\n", "        \n", "        # 處理請求資料 (B 欄位)\n", "        if 'request_data' in request['data']:\n", "            process_b_field(request['data']['request_data'], record)\n", "        \n", "        # 處理回覆資料\n", "        if reply:\n", "            for key, value in reply['data'].items():\n", "                if key != 'service_name':\n", "                    record[f'Rply_{key}'] = value\n", "        else:\n", "            # 如果沒有回覆，填入空值\n", "            if request['data'].get('request_data'):\n", "                for key in request['data']['request_data'].keys():\n", "                    if not isinstance(request['data']['request_data'][key], list):\n", "                        record[f'Rply_{key}'] = None\n", "        \n", "        grouped_data[a_type].append(record)\n", "    \n", "    return grouped_data\n", "\n", "def create_excel(grouped_data, output_path):\n", "    \"\"\"創建 Excel 檔案\"\"\"\n", "    try:\n", "        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:\n", "            for a_type, records in grouped_data.items():\n", "                if not records:\n", "                    continue\n", "                \n", "                # 確保所有記錄有相同的欄位\n", "                all_keys = set()\n", "                for record in records:\n", "                    all_keys.update(record.keys())\n", "                \n", "                # 填充缺失的欄位\n", "                processed_records = []\n", "                for record in records:\n", "                    processed_record = {key: record.get(key, None) for key in all_keys}\n", "                    processed_records.append(processed_record)\n", "                \n", "                # 創建 DataFrame\n", "                df = pd.DataFrame(processed_records)\n", "                \n", "                # 重新排列欄位順序\n", "                timestamp_cols = ['Rqst時間', 'Rply時間', '處理時間(秒)', 'service_name']\n", "                request_data_cols = sorted([col for col in df.columns if col.startswith('request_data_')])\n", "                request_cols = sorted([col for col in df.columns if col.startswith('Rqst_')])\n", "                reply_cols = sorted([col for col in df.columns if col.startswith('Rply_')])\n", "                other_cols = sorted([col for col in df.columns \n", "                                   if col not in timestamp_cols \n", "                                   and col not in request_data_cols\n", "                                   and col not in request_cols \n", "                                   and col not in reply_cols\n", "                                   and col != 'request_data'])\n", "                \n", "                # 重新排序欄位\n", "                cols_order = (timestamp_cols + \n", "                            (['request_data'] if 'request_data' in df.columns else []) +\n", "                            request_data_cols + \n", "                            request_cols + \n", "                            reply_cols + \n", "                            other_cols)\n", "                \n", "                # 只選擇存在的欄位\n", "                cols_order = [col for col in cols_order if col in df.columns]\n", "                df = df[cols_order]\n", "                \n", "                # 使用 A 類型作為工作表名稱，確保名稱有效\n", "                sheet_name = f'Type_{a_type}'\n", "                sheet_name = re.sub(r'[\\[\\]:*?/\\\\]', '_', sheet_name)  # 移除非法字符\n", "                sheet_name = sheet_name[:31]  # Excel工作表名稱限制\n", "                \n", "                # 寫入工作表\n", "                df.to_excel(writer, sheet_name=sheet_name, index=False)\n", "                \n", "    except Exception as e:\n", "        logging.error(f\"創建 Excel 時發生錯誤: {str(e)}\")\n", "        raise\n", "\n", "def process_log_file(input_path, output_path):\n", "    \"\"\"主要處理函數\"\"\"\n", "    print(\"開始處理日誌檔案...\")\n", "    \n", "    # 解析日誌檔案\n", "    entries = parse_log_file(input_path)\n", "    if not entries:\n", "        print(\"未找到有效的記錄\")\n", "        return\n", "    \n", "    # 處理記錄\n", "    grouped_data = process_entries(entries)\n", "    if not grouped_data:\n", "        print(\"無法處理記錄\")\n", "        return\n", "    \n", "    # 創建 Excel 檔案\n", "    print(\"\\n正在產生 Excel 檔案...\")\n", "    create_excel(grouped_data, output_path)\n", "    \n", "    print(f\"\\n處理完成，結果已儲存至 {output_path}\")\n", "\n", "\n", "import re\n", "import pandas as pd\n", "from datetime import datetime\n", "\n", "def parse_log_entry3(log_lines):\n", "    data = {\n", "        'timestamp': None,\n", "        'folder': None,\n", "        'file_count': None,\n", "        'services': {}\n", "    }\n", "    \n", "    for line in log_lines:\n", "        match = re.match(r'(\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2},\\d{3}) - INFO - (.*)', line)\n", "        if not match:\n", "            continue\n", "            \n", "        timestamp, message = match.groups()\n", "        \n", "        folder_match = re.match(r'開始處理資料夾: (.+)', message)\n", "        if folder_match:\n", "            # 修改：只取得倒數第二個路徑部分\n", "            folder_parts = folder_match.group(1).split('\\\\')\n", "            data['folder'] = folder_parts[-2] if len(folder_parts) >= 2 else folder_match.group(1)\n", "            data['timestamp'] = timestamp\n", "            continue\n", "            \n", "        file_count_match = re.match(r'找到 (\\d+) 個檔案', message)\n", "        if file_count_match:\n", "            data['file_count'] = int(file_count_match.group(1))\n", "            continue\n", "            \n", "        # 解析服務記錄數量\n", "        service_match = re.match(r'服務 (.+): (\\d+) 筆記錄', message)\n", "        if service_match:\n", "            service_name, count = service_match.groups()\n", "            data['services'][service_name] = int(count)\n", "            \n", "    return data\n", "\n", "def process_log_file3(log_file_path):\n", "    entries = []\n", "    current_entry = []\n", "    \n", "    with open(log_file_path, 'r', encoding='big5') as f:\n", "        lines = f.readlines()\n", "        \n", "    for line in lines:\n", "        if '開始處理資料夾' in line:\n", "            if current_entry:\n", "                entry_data = parse_log_entry3(current_entry)\n", "                if entry_data['timestamp']:  # 確保有有效數據\n", "                    entries.append(entry_data)\n", "            current_entry = [line]\n", "        else:\n", "            current_entry.append(line)\n", "    \n", "    # 處理最後一個entry\n", "    if current_entry:\n", "        entry_data = parse_log_entry3(current_entry)\n", "        if entry_data['timestamp']:\n", "            entries.append(entry_data)\n", "    \n", "    return entries\n", "\n", "def create_excel3(entries, output_file):\n", "    # 獲取所有可能的服務名稱\n", "    all_services = set()\n", "    for entry in entries:\n", "        all_services.update(entry['services'].keys())\n", "    \n", "    # 準備DataFrame數據\n", "    data = []\n", "    for entry in entries:\n", "        row = {\n", "            '處理時間': entry['timestamp'],\n", "            '資料夾名稱': entry['folder'],\n", "            '檔案數量': entry['file_count']\n", "        }\n", "        # 添加各服務的記錄數量\n", "        for service in all_services:\n", "            row[service] = entry['services'].get(service, 0)\n", "        data.append(row)\n", "    \n", "    # 創建DataFrame並寫入Excel\n", "    df = pd.DataFrame(data)\n", "    df.to_excel(output_file, index=False)\n", "    print(\"DONE!!!!\")\n", "\n", "def process_week_data(week: int, folders: List[str] = None):\n", "    \"\"\"處理指定週別的資料\n", "    \n", "    Args:\n", "        week: 週別編號\n", "        folders: 要處理的資料夾列表，如果為None則處理所有aulink_folder\n", "    \"\"\"\n", "    setup_logging()\n", "    \n", "    if folders is None:\n", "        folders = aulink_folder\n", "    \n", "    start_date, end_date = get_date_range_for_week(week)\n", "    logging.info(f\"開始處理週別 W{week} ({start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')})\")\n", "    \n", "    try:\n", "        # 處理每個資料夾\n", "        for folder in folders:\n", "            base_folder = fr\"\\\\k5dcnas02\\IME\\AAS_LOG\\{folder}\"\n", "            logging.info(f\"開始處理資料夾: {folder}\")\n", "            process_folder(str(base_folder), week)\n", "            \n", "    except Exception as e:\n", "        logging.error(f\"處理週別資料時發生錯誤: {str(e)}\")\n", "\n", "def main():\n", "    \"\"\"主程式 - 處理當前週別\"\"\"\n", "    today = datetime.now()\n", "    today_str = today.strftime('%Y%m%d')\n", "    current_week = get_week_number(today_str)\n", "    \n", "    # 處理上一週的資料\n", "    process_week = current_week - 1\n", "    process_week_data(process_week)\n", "\n", "def process_multiple_weeks(start_week: int, end_week: int, folders: List[str] = None):\n", "    \"\"\"處理多個週別的資料\n", "    \n", "    Args:\n", "        start_week: 開始週別\n", "        end_week: 結束週別（包含）\n", "        folders: 要處理的資料夾列表\n", "    \"\"\"\n", "    for week in range(start_week, end_week + 1):\n", "        logging.info(f\"\\n{'='*50}\")\n", "        logging.info(f\"開始處理週別 W{week}\")\n", "        logging.info(f\"{'='*50}\")\n", "        process_week_data(week, folders)\n", "\n", "if __name__ == \"__main__\":\n", "    # 可以選擇以下其中一種執行方式：\n", "    \n", "    # 1. 處理當前週別（預設）\n", "    main()\n", "    \n", "    # 2. 處理特定週別\n", "    # process_week_data(25)\n", "    \n", "    # 3. 處理多個週別\n", "    # process_multiple_weeks(17, 25)\n", "    \n", "    # 4. 處理特定資料夾的特定週別\n", "    # process_week_data(25, ['DFLM_HKT-53001', 'DFLM_HKT-53002'])"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.19"}}, "nbformat": 4, "nbformat_minor": 5}