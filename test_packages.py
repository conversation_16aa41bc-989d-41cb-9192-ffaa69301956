#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試所需套件是否可用
"""

def test_imports():
    """測試導入所需套件"""
    try:
        import json
        print("✓ json 模組可用")
    except ImportError as e:
        print(f"✗ json 模組錯誤: {e}")
    
    try:
        import logging
        print("✓ logging 模組可用")
    except ImportError as e:
        print(f"✗ logging 模組錯誤: {e}")
    
    try:
        from datetime import datetime, timedelta
        print("✓ datetime 模組可用")
    except ImportError as e:
        print(f"✗ datetime 模組錯誤: {e}")
    
    try:
        from pathlib import Path
        print("✓ pathlib 模組可用")
    except ImportError as e:
        print(f"✗ pathlib 模組錯誤: {e}")
    
    try:
        from concurrent.futures import ThreadPoolExecutor, as_completed
        print("✓ concurrent.futures 模組可用")
    except ImportError as e:
        print(f"✗ concurrent.futures 模組錯誤: {e}")
    
    try:
        from typing import List, Tuple, Optional, Dict, Any
        print("✓ typing 模組可用")
    except ImportError as e:
        print(f"✗ typing 模組錯誤: {e}")
    
    # 測試需要安裝的套件
    try:
        import pandas as pd
        print("✓ pandas 套件可用")
    except ImportError as e:
        print(f"✗ pandas 套件需要安裝: {e}")
    
    try:
        from tqdm import tqdm
        print("✓ tqdm 套件可用")
    except ImportError as e:
        print(f"✗ tqdm 套件需要安裝: {e}")
    
    try:
        import openpyxl
        print("✓ openpyxl 套件可用")
    except ImportError as e:
        print(f"✗ openpyxl 套件需要安裝: {e}")

def test_basic_functionality():
    """測試基本功能"""
    print("\n測試基本功能:")
    
    # 測試日期計算
    from datetime import datetime, timedelta
    today = datetime.now()
    print(f"今日日期: {today.strftime('%Y-%m-%d')}")
    
    # 測試路徑
    from pathlib import Path
    test_path = Path("test_folder")
    print(f"測試路徑: {test_path}")
    print(f"路徑存在: {test_path.exists()}")
    
    # 測試週別計算
    def get_week_number(date_str: str) -> int:
        """根據日期取得週別"""
        date = datetime.strptime(date_str, '%Y%m%d')
        base_date = datetime.strptime('20250202', '%Y%m%d')  # W6 開始日期
        days_diff = (date - base_date).days
        week_diff = days_diff // 7
        return week_diff + 6  # 從 W6 開始
    
    today_str = today.strftime('%Y%m%d')
    current_week = get_week_number(today_str)
    print(f"當前週別: W{current_week}")
    print(f"處理週別: W{current_week - 1}")

if __name__ == "__main__":
    print("開始測試套件...")
    test_imports()
    test_basic_functionality()
    print("\n測試完成！")
