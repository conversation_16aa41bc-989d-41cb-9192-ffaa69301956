#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
週別處理程式 - 修正版
整合文字替換和 Excel 資料處理功能
"""

import os
import json
import re
import logging
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
from datetime import datetime, timedelta
from typing import List, Dict, Any, Tuple, Optional
import pandas as pd

# 資料夾列表
AULINK_FOLDERS = [
    'DFLM_HKT-53001', 'DFLM_HKT-53002', 'DFLM_HKT-53003',
    'DV_V_AP-53201', 'DV_V_AP-53202', 'DV_V_AP-53301',
    'LA_NM-53001', 'LA_NM-53002', 'LA_NM-53003', 'LA_NM-53004',
    'MEC_FK-53101', 'MEC_FK-53301', 'SMVC_RH600-53002'
]

# 基礎路徑配置
BASE_LOG_PATH = r"\\k5dcnas02\IME\AAS_LOG"
BASE_OUTPUT_PATH = r"\\k5dcnas02\IME\個人資料夾\VICTOR\對對對對對\MO攔檢率\Rowdata"

# 預設替換規則
DEFAULT_REPLACEMENTS = [
    ('請按下""警報復歸""鈕', "請按下(警報復歸)鈕"),
    ('請按下警報復歸"鈕"', "請按下(警報復歸)鈕"), 
    ('"請按下""警報復歸""鈕"', "請按下(警報復歸)鈕"),
    ("請按下\"警報復歸\"鈕", "請按下(警報復歸)鈕"),
    ('"請按下(警報復歸)鈕"', "請按下(警報復歸)鈕")
]

def setup_logging():
    """設定日誌配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('byweek2_processor.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def get_week_number(date_str: str) -> int:
    """根據日期取得週別"""
    date = datetime.strptime(date_str, '%Y%m%d')
    base_date = datetime.strptime('20250202', '%Y%m%d')  # W6 開始日期
    days_diff = (date - base_date).days
    week_diff = days_diff // 7
    return week_diff + 6  # 從 W6 開始

def get_date_range_for_week(week: int) -> Tuple[datetime, datetime]:
    """取得指定週別的日期範圍"""
    base_date = datetime.strptime('20250202', '%Y%m%d')  # W6 開始日期
    days_to_add = (week - 6) * 7  # 從 W6 開始計算
    start_date = base_date + timedelta(days=days_to_add)
    end_date = start_date + timedelta(days=6)
    return start_date, end_date

def get_current_week() -> int:
    """取得當前週別"""
    today = datetime.now()
    today_str = today.strftime('%Y%m%d')
    return get_week_number(today_str)

def collect_week_files(folder: str, week: int) -> List[Path]:
    """收集指定週別的所有檔案"""
    start_date, end_date = get_date_range_for_week(week)
    
    # 獲取所有相關月份
    months_to_check = set()
    current_date = start_date
    while current_date <= end_date:
        months_to_check.add(current_date.strftime('%Y%m'))
        current_date += timedelta(days=1)
    
    logging.info(f"資料夾 {folder} 需要檢查的月份: {sorted(months_to_check)}")
    
    # 收集所有相關月份的檔案
    all_files = []
    base_folder = Path(BASE_LOG_PATH) / folder
    
    for month in months_to_check:
        month_folder = base_folder / month
        if month_folder.exists():
            month_files = list(month_folder.glob('*.log'))
            all_files.extend(month_files)
            logging.info(f"  月份 {month}: 找到 {len(month_files)} 個檔案")
        else:
            logging.warning(f"  月份 {month}: 資料夾不存在")
    
    # 過濾出該週的檔案
    week_files = []
    for f in all_files:
        try:
            file_date_str = f.stem.split('_')[-1]
            file_date = datetime.strptime(file_date_str, '%Y%m%d')
            if start_date <= file_date <= end_date:
                week_files.append(f)
        except (ValueError, IndexError) as e:
            logging.warning(f"無法解析檔案日期: {f.name}, 錯誤: {e}")
            continue
    
    logging.info(f"週別 W{week} 符合的檔案數量: {len(week_files)}")
    return week_files

def process_single_file_text(file_path: Path, replacements: List[Tuple[str, str]]) -> Tuple[bool, Optional[Tuple[str, str]]]:
    """處理單個檔案的文字替換"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        new_content = content
        for old, new in replacements:
            new_content = new_content.replace(old, new)
            
        if new_content != content:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(new_content)
                
        return True, None
        
    except Exception as e:
        return False, (str(file_path), str(e))

def process_text_replacement_by_week(week: int, replacements: List[Tuple[str, str]], folders: Optional[List[str]] = None) -> Tuple[int, List[Tuple[str, str]]]:
    """週別文字替換處理"""
    if folders is None:
        folders = AULINK_FOLDERS
    
    start_date, end_date = get_date_range_for_week(week)
    logging.info(f"開始處理週別 W{week} 文字替換 ({start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')})")
    
    total_processed = 0
    total_failures = []
    
    for folder in folders:
        logging.info(f"處理資料夾: {folder}")
        
        week_files = collect_week_files(folder, week)
        if not week_files:
            logging.warning(f"週別 W{week} 沒有找到 {folder} 的檔案")
            continue
        
        folder_processed = 0
        folder_failures = []
        
        for file_path in week_files:
            success, result = process_single_file_text(file_path, replacements)
            if success:
                folder_processed += 1
            else:
                folder_failures.append(result)
        
        logging.info(f"{folder} 處理完成: 成功 {folder_processed} 個檔案")
        if folder_failures:
            logging.warning(f"{folder} 處理失敗: {len(folder_failures)} 個檔案")
        
        total_processed += folder_processed
        total_failures.extend(folder_failures)
    
    logging.info(f"週別 W{week} 文字替換完成: 總共處理 {total_processed} 個檔案")
    return total_processed, total_failures

# Excel 處理功能
def parse_log_entry(lines):
    """解析單個日誌條目"""
    try:
        first_line = lines[0].strip()
        timestamp_match = re.match(r'(\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2}:\d{2}\.\d{3})', first_line)
        if not timestamp_match:
            return None

        timestamp = datetime.strptime(timestamp_match.group(1), '%Y/%m/%d %H:%M:%S.%f')
        json_text = ''.join(lines[1:])
        data = json.loads(json_text)

        return {
            'timestamp': timestamp,
            'data': data,
            'type': 'request' if '[Rqst]' in first_line else 'reply'
        }
    except Exception as e:
        logging.debug(f"解析錯誤: {str(e)}")
        return None

def parse_log_file(file_path):
    """解析整個日誌檔案"""
    entries = []
    current_entry = []

    try:
        with open(file_path, 'r', encoding='utf-8-sig') as file:
            lines = file.readlines()

        for line in lines:
            line = line.rstrip()

            if re.match(r'\d{4}/\d{2}/\d{2}', line):
                if current_entry:
                    parsed = parse_log_entry(current_entry)
                    if parsed:
                        entries.append(parsed)
                current_entry = [line]
            else:
                if line:
                    current_entry.append(line)

        if current_entry:
            parsed = parse_log_entry(current_entry)
            if parsed:
                entries.append(parsed)

    except Exception as e:
        logging.error(f"檔案讀取錯誤 {file_path}: {str(e)}")
        return []

    return entries

def pair_requests_replies(entries):
    """配對請求和回覆"""
    pairs = []
    request_buffer = {}

    for entry in entries:
        if entry['type'] == 'request':
            service_name = entry['data'].get('service_name')
            if service_name:
                request_buffer[service_name] = entry
        elif entry['type'] == 'reply':
            service_name = entry['data'].get('service_name')
            if service_name and service_name in request_buffer:
                pairs.append({
                    'request': request_buffer.pop(service_name),
                    'reply': entry
                })

    for request in request_buffer.values():
        pairs.append({
            'request': request,
            'reply': None
        })

    return pairs

def process_single_file_excel(file_path: Path) -> Dict[str, List[Dict[str, Any]]]:
    """處理單個檔案的 Excel 資料"""
    results = {}

    try:
        entries = parse_log_file(file_path)
        if not entries:
            return results

        pairs = pair_requests_replies(entries)

        for pair in pairs:
            request = pair['request']
            reply = pair['reply']

            service_name = request['data'].get('service_name', 'Unknown')

            if service_name not in results:
                results[service_name] = []

            record = {
                'Rqst時間': request['timestamp'],
                'Rply時間': reply['timestamp'] if reply else '無回復資料',
                '處理時間(秒)': (reply['timestamp'] - request['timestamp']).total_seconds() if reply else None,
                'service_name': service_name
            }

            # 處理請求資料
            request_data = request['data'].get('request_data')
            if isinstance(request_data, dict):
                for key, value in request_data.items():
                    record[f'request_data_{key}'] = value
            elif request_data is not None:
                record['request_data'] = request_data

            # 處理回覆資料
            if reply:
                reply_data = reply['data'].get('reply_data')
                if isinstance(reply_data, dict):
                    for key, value in reply_data.items():
                        record[f'reply_data_{key}'] = value
                elif reply_data is not None:
                    record['reply_data'] = reply_data

            results[service_name].append(record)

    except Exception as e:
        logging.error(f"處理檔案 {file_path} 時發生錯誤: {e}")

    return results
