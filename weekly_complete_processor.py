#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整週別處理程式
整合文字替換和 Excel 資料處理功能
"""

import logging
import sys
from datetime import datetime, timedelta
from pathlib import Path

# 導入子模組
try:
    from text_replacement_processor import (
        process_text_replacement_by_week, 
        DEFAULT_REPLACEMENTS,
        get_current_week,
        get_date_range_for_week,
        setup_logging as setup_text_logging
    )
    from weekly_processor_fixed import (
        process_excel_data_by_week,
        setup_logging as setup_excel_logging
    )
except ImportError as e:
    print(f"導入錯誤: {e}")
    print("請確保 text_replacement_processor.py 和 weekly_processor_fixed.py 在同一目錄")
    sys.exit(1)

def setup_logging():
    """設定整合日誌配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('weekly_complete_processor.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def process_complete_week(week: int, folders: list = None):
    """完整處理指定週別的資料"""
    setup_logging()
    
    start_date, end_date = get_date_range_for_week(week)
    logging.info(f"開始完整處理週別 W{week} ({start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')})")
    
    try:
        # 第一步：文字替換處理
        logging.info("="*60)
        logging.info("第一步：開始文字替換處理")
        logging.info("="*60)
        
        total_processed, failures = process_text_replacement_by_week(week, DEFAULT_REPLACEMENTS, folders)
        
        if failures:
            logging.warning(f"文字替換有 {len(failures)} 個檔案處理失敗")
            for file_path, error in failures:
                logging.warning(f"失敗檔案: {file_path}, 錯誤: {error}")
        
        logging.info(f"文字替換完成：處理了 {total_processed} 個檔案")
        
        # 第二步：Excel 資料處理
        logging.info("="*60)
        logging.info("第二步：開始 Excel 資料處理")
        logging.info("="*60)
        
        process_excel_data_by_week(week, folders)
        
        # 完成
        logging.info("="*60)
        logging.info(f"週別 W{week} 完整處理完成！")
        logging.info(f"✅ 文字替換：處理了 {total_processed} 個檔案")
        logging.info(f"✅ Excel 輸出：已生成對應的 Excel 檔案")
        logging.info("="*60)
        
        return True
        
    except Exception as e:
        logging.error(f"完整處理執行錯誤: {str(e)}")
        return False

def process_text_only(week: int, folders: list = None):
    """只處理文字替換"""
    setup_logging()
    
    start_date, end_date = get_date_range_for_week(week)
    logging.info(f"開始處理週別 W{week} 文字替換 ({start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')})")
    
    try:
        total_processed, failures = process_text_replacement_by_week(week, DEFAULT_REPLACEMENTS, folders)
        
        if failures:
            logging.warning(f"文字替換有 {len(failures)} 個檔案處理失敗")
            for file_path, error in failures:
                logging.warning(f"失敗檔案: {file_path}, 錯誤: {error}")
        
        logging.info(f"文字替換完成：處理了 {total_processed} 個檔案")
        return True
        
    except Exception as e:
        logging.error(f"文字替換執行錯誤: {str(e)}")
        return False

def process_excel_only(week: int, folders: list = None):
    """只處理 Excel 輸出"""
    setup_logging()
    
    start_date, end_date = get_date_range_for_week(week)
    logging.info(f"開始處理週別 W{week} Excel 資料 ({start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')})")
    
    try:
        process_excel_data_by_week(week, folders)
        logging.info(f"Excel 處理完成")
        return True
        
    except Exception as e:
        logging.error(f"Excel 處理執行錯誤: {str(e)}")
        return False

def main():
    """主程式 - 處理上一週的完整資料"""
    current_week = get_current_week()
    process_week = current_week - 1
    
    print(f"開始處理上一週 (W{process_week}) 的完整資料...")
    success = process_complete_week(process_week)
    
    if success:
        print("✅ 完整處理成功！")
    else:
        print("❌ 處理過程中發生錯誤，請查看日誌")

def show_usage():
    """顯示使用說明"""
    print("週別完整處理程式")
    print("=" * 50)
    print("用法:")
    print("  python weekly_complete_processor.py                    # 處理上一週完整資料")
    print("  python weekly_complete_processor.py week=25            # 處理指定週別完整資料")
    print("  python weekly_complete_processor.py text week=25       # 只處理文字替換")
    print("  python weekly_complete_processor.py excel week=25      # 只處理 Excel 輸出")
    print("  python weekly_complete_processor.py help               # 顯示說明")
    print()
    print("功能說明:")
    print("  1. 文字替換：修正警報復歸按鈕文字格式")
    print("  2. Excel 輸出：生成週別資料分析檔案")
    print("  3. 預設處理順序：先文字替換，再 Excel 輸出")
    print()
    print("輸出位置:")
    print("  - 日誌檔案：weekly_complete_processor.log")
    print("  - Excel 檔案：\\\\k5dcnas02\\IME\\個人資料夾\\VICTOR\\對對對對對\\MO攔檢率\\Rowdata\\")

if __name__ == "__main__":
    if len(sys.argv) == 1:
        # 沒有參數，執行預設處理
        main()
    elif len(sys.argv) == 2:
        arg = sys.argv[1]
        if arg == "help":
            show_usage()
        elif arg.startswith("week="):
            # 處理指定週別完整資料
            week_num = int(arg.split("=")[1])
            print(f"開始處理週別 W{week_num} 的完整資料...")
            success = process_complete_week(week_num)
            if success:
                print("✅ 完整處理成功！")
            else:
                print("❌ 處理過程中發生錯誤，請查看日誌")
        else:
            print("❌ 無效的參數")
            show_usage()
    elif len(sys.argv) == 3:
        mode = sys.argv[1]
        arg = sys.argv[2]
        
        if not arg.startswith("week="):
            print("❌ 第二個參數必須是 week=數字")
            show_usage()
            sys.exit(1)
        
        week_num = int(arg.split("=")[1])
        
        if mode == "text":
            print(f"開始處理週別 W{week_num} 的文字替換...")
            success = process_text_only(week_num)
            if success:
                print("✅ 文字替換處理成功！")
            else:
                print("❌ 文字替換處理失敗，請查看日誌")
        elif mode == "excel":
            print(f"開始處理週別 W{week_num} 的 Excel 輸出...")
            success = process_excel_only(week_num)
            if success:
                print("✅ Excel 處理成功！")
            else:
                print("❌ Excel 處理失敗，請查看日誌")
        else:
            print("❌ 無效的模式，只支援 'text' 或 'excel'")
            show_usage()
    else:
        print("❌ 參數過多")
        show_usage()
