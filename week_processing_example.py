# 週別資料處理範例
# 這個文件展示了如何使用修改後的週別處理功能

from datetime import datetime, timedelta
from pathlib import Path
import logging
from typing import List

# 資料夾列表
aulink_folder = ['DFLM_HKT-53001','DFLM_HKT-53002', 'DFLM_HKT-53003',
 'DV_V_AP-53201', 'DV_V_AP-53202', 'DV_V_AP-53301',
 'LA_NM-53001', 'LA_NM-53002', 'LA_NM-53003',
 'LA_NM-53004', 'MEC_FK-53101', 'MEC_FK-53301', 'SMVC_RH600-53002']

def get_week_number(date_str):
    """根據日期取得週別"""
    date = datetime.strptime(date_str, '%Y%m%d')
    base_date = datetime.strptime('20250202', '%Y%m%d')  # W6 開始日期
    days_diff = (date - base_date).days
    week_diff = days_diff // 7
    return week_diff + 6  # 從 W6 開始

def get_date_range_for_week(week):
    """取得指定週別的日期範圍"""
    base_date = datetime.strptime('20250202', '%Y%m%d')  # W6 開始日期
    days_to_add = (week - 6) * 7  # 從 W6 開始計算
    start_date = base_date + timedelta(days=days_to_add)
    end_date = start_date + timedelta(days=6)
    return start_date, end_date

def process_week_data(week: int, folders: List[str] = None):
    """處理指定週別的資料
    
    主要改進：
    1. 自動跨月份收集檔案 - 一週可能跨越兩個月份
    2. 根據週別日期範圍過濾檔案 - 精確匹配週別範圍
    3. 更好的錯誤處理 - 處理檔案名稱解析錯誤
    
    Args:
        week: 週別編號
        folders: 要處理的資料夾列表，如果為None則處理所有aulink_folder
    """
    if folders is None:
        folders = aulink_folder
    
    start_date, end_date = get_date_range_for_week(week)
    print(f"開始處理週別 W{week} ({start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')})")
    
    for folder in folders:
        print(f"\n處理資料夾: {folder}")
        
        # 設定路徑
        base_folder = Path(fr"\\k5dcnas02\IME\AAS_LOG\{folder}")
        
        # 獲取所有相關月份的檔案
        all_files = []
        months_to_check = set()
        current_date = start_date
        while current_date <= end_date:
            months_to_check.add(current_date.strftime('%Y%m'))
            current_date += timedelta(days=1)
        
        print(f"  需要檢查的月份: {sorted(months_to_check)}")
        
        # 收集所有相關月份的檔案
        for month in months_to_check:
            month_folder = base_folder / month  # 修改：直接在folder下找月份資料夾
            if month_folder.exists():
                log_files = list(month_folder.glob('*.log'))
                all_files.extend(log_files)
                print(f"  月份 {month}: 找到 {len(log_files)} 個檔案")
            else:
                print(f"  月份 {month}: 資料夾不存在")
        
        if not all_files:
            print(f"  警告：找不到週別 W{week} 的相關檔案")
            continue
        
        # 過濾出該週的檔案
        files = []
        for f in all_files:
            try:
                # 從檔案名稱中提取日期
                file_date_str = f.stem.split('_')[-1]
                file_date = datetime.strptime(file_date_str, '%Y%m%d')
                if start_date <= file_date <= end_date:
                    files.append(f)
            except (ValueError, IndexError) as e:
                print(f"  警告：無法解析檔案日期: {f.name}, 錯誤: {e}")
                continue
        
        print(f"  週別 W{week} 符合的檔案數量: {len(files)}")
        for f in files:
            print(f"    - {f.name}")
        
        if not files:
            print(f"  警告：週別 W{week} 沒有符合的檔案")

def process_multiple_weeks(start_week: int, end_week: int, folders: List[str] = None):
    """處理多個週別的資料
    
    Args:
        start_week: 開始週別
        end_week: 結束週別（包含）
        folders: 要處理的資料夾列表
    """
    for week in range(start_week, end_week + 1):
        print(f"\n{'='*50}")
        print(f"開始處理週別 W{week}")
        print(f"{'='*50}")
        process_week_data(week, folders)

def main():
    """主程式 - 處理當前週別"""
    today = datetime.now()
    today_str = today.strftime('%Y%m%d')
    current_week = get_week_number(today_str)
    
    # 處理上一週的資料
    process_week = current_week - 1
    process_week_data(process_week)

if __name__ == "__main__":
    print("週別資料處理範例")
    print("="*50)
    
    # 展示週別對應的日期範圍
    print("\n週別對應日期範圍範例：")
    for week in [23, 24, 25, 26]:
        start_date, end_date = get_date_range_for_week(week)
        print(f"週別 W{week}: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
    
    print("\n可以選擇以下其中一種執行方式：")
    print("1. process_week_data(25)  # 處理第25週")
    print("2. process_multiple_weeks(17, 25)  # 處理第17週到第25週")
    print("3. process_week_data(25, ['DFLM_HKT-53001'])  # 處理特定資料夾的第25週")
    
    # 取消註解以下任一行來執行
    # process_week_data(25, ['DFLM_HKT-53001'])  # 測試單一資料夾
