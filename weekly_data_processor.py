#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
週別資料處理程式
整合 Excel 資料輸出和文字替換功能，全部以週別為基礎進行處理
"""

import os
import json
import logging
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
from typing import List, Tuple, Optional, Dict, Any
import re

# 設定日誌
def setup_logging():
    """設定日誌配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('weekly_processor.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

# 資料夾列表
AULINK_FOLDERS = [
    'DFLM_HKT-53001', 'DFLM_HKT-53002', 'DFLM_HKT-53003',
    'DV_V_AP-53201', 'DV_V_AP-53202', 'DV_V_AP-53301',
    'LA_NM-53001', 'LA_NM-53002', 'LA_NM-53003', 'LA_NM-53004',
    'MEC_FK-53101', 'MEC_FK-53301', 'SMVC_RH600-53002'
]

# 基礎路徑配置
BASE_LOG_PATH = r"\\k5dcnas02\IME\AAS_LOG"
BASE_OUTPUT_PATH = r"\\k5dcnas02\IME\個人資料夾\VICTOR\對對對對對\MO攔檢率\Rowdata"

# 週別計算函數
def get_week_number(date_str: str) -> int:
    """根據日期取得週別"""
    date = datetime.strptime(date_str, '%Y%m%d')
    base_date = datetime.strptime('20250202', '%Y%m%d')  # W6 開始日期
    days_diff = (date - base_date).days
    week_diff = days_diff // 7
    return week_diff + 6  # 從 W6 開始

def get_date_range_for_week(week: int) -> Tuple[datetime, datetime]:
    """取得指定週別的日期範圍"""
    base_date = datetime.strptime('20250202', '%Y%m%d')  # W6 開始日期
    days_to_add = (week - 6) * 7  # 從 W6 開始計算
    start_date = base_date + timedelta(days=days_to_add)
    end_date = start_date + timedelta(days=6)
    return start_date, end_date

def get_current_week() -> int:
    """取得當前週別"""
    today = datetime.now()
    today_str = today.strftime('%Y%m%d')
    return get_week_number(today_str)

# 檔案收集函數
def collect_week_files(folder: str, week: int) -> List[Path]:
    """收集指定週別的所有檔案"""
    start_date, end_date = get_date_range_for_week(week)
    
    # 獲取所有相關月份
    months_to_check = set()
    current_date = start_date
    while current_date <= end_date:
        months_to_check.add(current_date.strftime('%Y%m'))
        current_date += timedelta(days=1)
    
    logging.info(f"資料夾 {folder} 需要檢查的月份: {sorted(months_to_check)}")
    
    # 收集所有相關月份的檔案
    all_files = []
    base_folder = Path(BASE_LOG_PATH) / folder
    
    for month in months_to_check:
        month_folder = base_folder / month
        if month_folder.exists():
            month_files = list(month_folder.glob('*.log'))
            all_files.extend(month_files)
            logging.info(f"  月份 {month}: 找到 {len(month_files)} 個檔案")
        else:
            logging.warning(f"  月份 {month}: 資料夾不存在")
    
    # 過濾出該週的檔案
    week_files = []
    for f in all_files:
        try:
            file_date_str = f.stem.split('_')[-1]
            file_date = datetime.strptime(file_date_str, '%Y%m%d')
            if start_date <= file_date <= end_date:
                week_files.append(f)
        except (ValueError, IndexError) as e:
            logging.warning(f"無法解析檔案日期: {f.name}, 錯誤: {e}")
            continue
    
    logging.info(f"週別 W{week} 符合的檔案數量: {len(week_files)}")
    return week_files

# 文字替換功能
def process_single_file_text_replacement(file_path: Path, replacements: List[Tuple[str, str]]) -> Tuple[bool, Optional[Tuple[str, str]]]:
    """處理單個檔案的文字替換"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        new_content = content
        for old, new in replacements:
            new_content = new_content.replace(old, new)
        
        if new_content != content:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(new_content)
        
        return True, None
    except Exception as e:
        return False, (str(file_path), str(e))

def process_text_replacement_by_week(week: int, replacements: List[Tuple[str, str]], folders: Optional[List[str]] = None) -> Tuple[int, List[Tuple[str, str]]]:
    """週別文字替換處理"""
    if folders is None:
        folders = AULINK_FOLDERS
    
    start_date, end_date = get_date_range_for_week(week)
    logging.info(f"開始處理週別 W{week} 文字替換 ({start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')})")
    
    total_processed = 0
    total_failures = []
    
    for folder in folders:
        logging.info(f"處理資料夾: {folder}")
        
        week_files = collect_week_files(folder, week)
        if not week_files:
            logging.warning(f"週別 W{week} 沒有找到 {folder} 的檔案")
            continue
        
        folder_processed = 0
        folder_failures = []
        
        for file_path in week_files:
            success, result = process_single_file_text_replacement(file_path, replacements)
            if success:
                folder_processed += 1
            else:
                folder_failures.append(result)
        
        logging.info(f"{folder} 處理完成: 成功 {folder_processed} 個檔案")
        if folder_failures:
            logging.warning(f"{folder} 處理失敗: {len(folder_failures)} 個檔案")
        
        total_processed += folder_processed
        total_failures.extend(folder_failures)
    
    logging.info(f"週別 W{week} 文字替換完成: 總共處理 {total_processed} 個檔案")
    return total_processed, total_failures

# Excel 資料處理功能
def parse_log_line(line: str) -> Optional[Dict[str, Any]]:
    """解析單行日誌"""
    try:
        parts = line.strip().split(' ', 2)
        if len(parts) < 3:
            return None
        
        date_part = parts[0]
        time_part = parts[1]
        message_part = parts[2]
        
        datetime_str = f"{date_part} {time_part}"
        
        if '[Recv]' in message_part:
            direction = 'Recv'
            content = message_part.split('[Recv]', 1)[1].strip()
        elif '[Send]' in message_part:
            direction = 'Send'
            content = message_part.split('[Send]', 1)[1].strip()
        else:
            return None
        
        # 解析 JSON 內容
        try:
            json_data = json.loads(content)
            return {
                'DateTime': datetime_str,
                'Direction': direction,
                'Content': json_data
            }
        except json.JSONDecodeError:
            return {
                'DateTime': datetime_str,
                'Direction': direction,
                'Content': content
            }
    
    except Exception as e:
        logging.debug(f"解析行失敗: {line[:100]}..., 錯誤: {e}")
        return None

def process_single_log_file(file_path: Path) -> Dict[str, List[Dict[str, Any]]]:
    """處理單個日誌檔案"""
    results = {}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            for line in file:
                parsed = parse_log_line(line)
                if parsed and isinstance(parsed['Content'], dict):
                    service_name = parsed['Content'].get('ServiceName', 'Unknown')
                    if service_name not in results:
                        results[service_name] = []
                    results[service_name].append(parsed)
    
    except Exception as e:
        logging.error(f"處理檔案 {file_path} 時發生錯誤: {e}")

    return results

def process_excel_data_by_week(week: int, folders: Optional[List[str]] = None, max_workers: int = 4) -> None:
    """週別 Excel 資料處理"""
    if folders is None:
        folders = AULINK_FOLDERS

    start_date, end_date = get_date_range_for_week(week)
    logging.info(f"開始處理週別 W{week} Excel 資料 ({start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')})")

    for folder in folders:
        logging.info(f"處理資料夾: {folder}")

        # 設定輸出路徑
        output_folder = Path(BASE_OUTPUT_PATH) / folder
        output_folder.mkdir(parents=True, exist_ok=True)

        year_suffix = start_date.strftime('%y')
        output_file = output_folder / f"{folder}_{year_suffix}_W{week}.xlsx"

        # 收集週別檔案
        week_files = collect_week_files(folder, week)
        if not week_files:
            logging.warning(f"週別 W{week} 沒有找到 {folder} 的檔案")
            continue

        logging.info(f"找到 {len(week_files)} 個檔案")

        # 多線程處理檔案
        all_results = {}
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_file = {executor.submit(process_single_log_file, file_path): file_path
                             for file_path in week_files}

            with tqdm(total=len(week_files), desc="處理檔案") as pbar:
                for future in as_completed(future_to_file):
                    file_results = future.result()

                    # 合併結果
                    for service_name, records in file_results.items():
                        if service_name not in all_results:
                            all_results[service_name] = []
                        all_results[service_name].extend(records)

                    pbar.update(1)

        # 合併處理結果
        logging.info("合併處理結果...")

        # 輸出統計
        for service_name, records in all_results.items():
            logging.info(f"服務 {service_name}: {len(records)} 筆記錄")

        # 寫入 Excel 檔案
        if all_results:
            logging.info(f"寫入 Excel 檔案: {output_file}")

            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                for service_name, records in all_results.items():
                    if records:
                        # 轉換為 DataFrame
                        df_data = []
                        for record in records:
                            row = {
                                'DateTime': record['DateTime'],
                                'Direction': record['Direction']
                            }

                            # 展開 Content 欄位
                            if isinstance(record['Content'], dict):
                                for key, value in record['Content'].items():
                                    row[key] = value
                            else:
                                row['Content'] = record['Content']

                            df_data.append(row)

                        df = pd.DataFrame(df_data)

                        # 限制工作表名稱長度
                        sheet_name = service_name[:31] if len(service_name) > 31 else service_name
                        df.to_excel(writer, sheet_name=sheet_name, index=False)

        logging.info(f"完成處理資料夾: {Path(BASE_LOG_PATH) / folder}")

def process_multiple_weeks_excel(start_week: int, end_week: int, folders: Optional[List[str]] = None) -> None:
    """處理多個週別的 Excel 資料"""
    for week in range(start_week, end_week + 1):
        logging.info(f"\n{'='*50}")
        logging.info(f"開始處理週別 W{week}")
        logging.info(f"{'='*50}")
        process_excel_data_by_week(week, folders)

def process_multiple_weeks_text(start_week: int, end_week: int, replacements: List[Tuple[str, str]], folders: Optional[List[str]] = None) -> None:
    """處理多個週別的文字替換"""
    for week in range(start_week, end_week + 1):
        logging.info(f"\n{'='*50}")
        logging.info(f"開始處理週別 W{week} 文字替換")
        logging.info(f"{'='*50}")
        process_text_replacement_by_week(week, replacements, folders)

# 預設替換規則
DEFAULT_REPLACEMENTS = [
    ('請按下""警報復歸""鈕', "請按下(警報復歸)鈕"),
    ('請按下警報復歸"鈕"', "請按下(警報復歸)鈕"),
    ('"請按下""警報復歸""鈕"', "請按下(警報復歸)鈕"),
    ("請按下\"警報復歸\"鈕", "請按下(警報復歸)鈕"),
    ('"請按下(警報復歸)鈕"', "請按下(警報復歸)鈕")
]

def main():
    """主程式 - 處理上一週的資料"""
    setup_logging()

    # 取得當前週別，處理上一週
    current_week = get_current_week()
    process_week = current_week - 1

    start_date, end_date = get_date_range_for_week(process_week)
    logging.info(f"開始處理週別 W{process_week} ({start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')})")

    try:
        # 1. 處理 Excel 資料輸出
        logging.info("="*60)
        logging.info("開始 Excel 資料處理")
        logging.info("="*60)
        process_excel_data_by_week(process_week)

        # 2. 處理文字替換
        logging.info("="*60)
        logging.info("開始文字替換處理")
        logging.info("="*60)
        process_text_replacement_by_week(process_week, DEFAULT_REPLACEMENTS)

        logging.info("="*60)
        logging.info(f"週別 W{process_week} 所有處理完成！")
        logging.info("="*60)

    except Exception as e:
        logging.error(f"主程式執行錯誤: {str(e)}")

if __name__ == "__main__":
    main()
