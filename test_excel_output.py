#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 Excel 輸出功能
"""

import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any
import pandas as pd

# 設定日誌
def setup_logging():
    """設定日誌配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('test_excel_output.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

# 基礎路徑配置
BASE_LOG_PATH = r"\\k5dcnas02\IME\AAS_LOG"
BASE_OUTPUT_PATH = r"\\k5dcnas02\IME\個人資料夾\VICTOR\對對對對對\MO攔檢率\Rowdata"

# 週別計算函數
def get_week_number(date_str: str) -> int:
    """根據日期取得週別"""
    date = datetime.strptime(date_str, '%Y%m%d')
    base_date = datetime.strptime('20250202', '%Y%m%d')  # W6 開始日期
    days_diff = (date - base_date).days
    week_diff = days_diff // 7
    return week_diff + 6  # 從 W6 開始

def get_date_range_for_week(week: int):
    """取得指定週別的日期範圍"""
    base_date = datetime.strptime('20250202', '%Y%m%d')  # W6 開始日期
    days_to_add = (week - 6) * 7  # 從 W6 開始計算
    start_date = base_date + timedelta(days=days_to_add)
    end_date = start_date + timedelta(days=6)
    return start_date, end_date

def get_current_week() -> int:
    """取得當前週別"""
    today = datetime.now()
    today_str = today.strftime('%Y%m%d')
    return get_week_number(today_str)

def parse_log_line(line: str):
    """解析單行日誌"""
    try:
        parts = line.strip().split(' ', 2)
        if len(parts) < 3:
            return None
        
        date_part = parts[0]
        time_part = parts[1]
        message_part = parts[2]
        
        datetime_str = f"{date_part} {time_part}"
        
        if '[Recv]' in message_part:
            direction = 'Recv'
            content = message_part.split('[Recv]', 1)[1].strip()
        elif '[Send]' in message_part:
            direction = 'Send'
            content = message_part.split('[Send]', 1)[1].strip()
        else:
            return None
        
        # 解析 JSON 內容
        try:
            json_data = json.loads(content)
            return {
                'DateTime': datetime_str,
                'Direction': direction,
                'Content': json_data
            }
        except json.JSONDecodeError:
            return {
                'DateTime': datetime_str,
                'Direction': direction,
                'Content': content
            }
    
    except Exception as e:
        logging.debug(f"解析行失敗: {line[:100]}..., 錯誤: {e}")
        return None

def process_single_log_file(file_path: Path):
    """處理單個日誌檔案"""
    results = {}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            for line in file:
                parsed = parse_log_line(line)
                if parsed and isinstance(parsed['Content'], dict):
                    service_name = parsed['Content'].get('ServiceName', 'Unknown')
                    if service_name not in results:
                        results[service_name] = []
                    results[service_name].append(parsed)
    
    except Exception as e:
        logging.error(f"處理檔案 {file_path} 時發生錯誤: {e}")
    
    return results

def collect_week_files(folder: str, week: int) -> List[Path]:
    """收集指定週別的所有檔案"""
    start_date, end_date = get_date_range_for_week(week)
    
    # 獲取所有相關月份
    months_to_check = set()
    current_date = start_date
    while current_date <= end_date:
        months_to_check.add(current_date.strftime('%Y%m'))
        current_date += timedelta(days=1)
    
    logging.info(f"資料夾 {folder} 需要檢查的月份: {sorted(months_to_check)}")
    
    # 收集所有相關月份的檔案
    all_files = []
    base_folder = Path(BASE_LOG_PATH) / folder
    
    for month in months_to_check:
        month_folder = base_folder / month
        if month_folder.exists():
            month_files = list(month_folder.glob('*.log'))
            all_files.extend(month_files)
            logging.info(f"  月份 {month}: 找到 {len(month_files)} 個檔案")
        else:
            logging.warning(f"  月份 {month}: 資料夾不存在")
    
    # 過濾出該週的檔案
    week_files = []
    for f in all_files:
        try:
            file_date_str = f.stem.split('_')[-1]
            file_date = datetime.strptime(file_date_str, '%Y%m%d')
            if start_date <= file_date <= end_date:
                week_files.append(f)
        except (ValueError, IndexError) as e:
            logging.warning(f"無法解析檔案日期: {f.name}, 錯誤: {e}")
            continue
    
    logging.info(f"週別 W{week} 符合的檔案數量: {len(week_files)}")
    return week_files

def test_excel_output_single_folder(folder: str, week: int):
    """測試單個資料夾的 Excel 輸出"""
    setup_logging()
    
    start_date, end_date = get_date_range_for_week(week)
    logging.info(f"測試資料夾 {folder} 週別 W{week} ({start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')})")
    
    # 設定輸出路徑
    output_folder = Path(BASE_OUTPUT_PATH) / folder
    logging.info(f"輸出資料夾: {output_folder}")
    
    # 檢查並創建輸出資料夾
    try:
        output_folder.mkdir(parents=True, exist_ok=True)
        logging.info("輸出資料夾創建成功")
    except Exception as e:
        logging.error(f"創建輸出資料夾失敗: {e}")
        return False
    
    # 設定輸出檔案名稱
    year_suffix = start_date.strftime('%y')
    output_file = output_folder / f"{folder}_{year_suffix}_W{week:02d}.xlsx"
    logging.info(f"輸出檔案: {output_file}")
    
    # 收集週別檔案
    week_files = collect_week_files(folder, week)
    if not week_files:
        logging.warning(f"週別 W{week} 沒有找到 {folder} 的檔案")
        return False
    
    logging.info(f"找到 {len(week_files)} 個檔案，開始處理...")
    
    # 處理檔案
    all_results = {}
    for i, file_path in enumerate(week_files[:3]):  # 只處理前3個檔案進行測試
        logging.info(f"處理檔案 {i+1}/{min(3, len(week_files))}: {file_path.name}")
        file_results = process_single_log_file(file_path)
        
        # 合併結果
        for service_name, records in file_results.items():
            if service_name not in all_results:
                all_results[service_name] = []
            all_results[service_name].extend(records)
    
    # 輸出統計
    logging.info("處理結果統計:")
    for service_name, records in all_results.items():
        logging.info(f"  服務 {service_name}: {len(records)} 筆記錄")
    
    # 寫入 Excel 檔案
    if all_results:
        try:
            logging.info(f"開始寫入 Excel 檔案: {output_file}")
            
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                for service_name, records in all_results.items():
                    if records:
                        # 轉換為 DataFrame
                        df_data = []
                        for record in records:
                            row = {
                                'DateTime': record['DateTime'],
                                'Direction': record['Direction']
                            }
                            
                            # 展開 Content 欄位
                            if isinstance(record['Content'], dict):
                                for key, value in record['Content'].items():
                                    row[key] = value
                            else:
                                row['Content'] = record['Content']
                            
                            df_data.append(row)
                        
                        df = pd.DataFrame(df_data)
                        
                        # 限制工作表名稱長度
                        sheet_name = service_name[:31] if len(service_name) > 31 else service_name
                        df.to_excel(writer, sheet_name=sheet_name, index=False)
                        logging.info(f"  工作表 '{sheet_name}' 寫入完成: {len(df)} 筆記錄")
            
            # 檢查檔案是否成功創建
            if output_file.exists():
                file_size = output_file.stat().st_size
                logging.info(f"Excel 檔案創建成功！檔案大小: {file_size} bytes")
                return True
            else:
                logging.error("Excel 檔案創建失敗！")
                return False
                
        except Exception as e:
            logging.error(f"寫入 Excel 檔案時發生錯誤: {e}")
            return False
    else:
        logging.warning("沒有資料可寫入 Excel 檔案")
        return False

if __name__ == "__main__":
    # 測試第一個資料夾的當前週別-1
    test_folder = "DFLM_HKT-53001"
    current_week = get_current_week()
    test_week = current_week - 1
    
    print(f"開始測試 Excel 輸出功能...")
    print(f"測試資料夾: {test_folder}")
    print(f"測試週別: W{test_week}")
    
    success = test_excel_output_single_folder(test_folder, test_week)
    
    if success:
        print("✅ Excel 輸出測試成功！")
    else:
        print("❌ Excel 輸出測試失敗！")
    
    print("詳細日誌請查看 test_excel_output.log")
