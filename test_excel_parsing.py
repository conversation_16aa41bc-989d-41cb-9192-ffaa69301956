#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修正後的 Excel 解析功能
"""

import json
import logging
import re
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any

# 設定日誌
def setup_logging():
    """設定日誌配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('test_excel_parsing.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

# 基礎路徑配置
BASE_LOG_PATH = r"\\k5dcnas02\IME\AAS_LOG"

# 週別計算函數
def get_week_number(date_str: str) -> int:
    """根據日期取得週別"""
    date = datetime.strptime(date_str, '%Y%m%d')
    base_date = datetime.strptime('20250202', '%Y%m%d')  # W6 開始日期
    days_diff = (date - base_date).days
    week_diff = days_diff // 7
    return week_diff + 6  # 從 W6 開始

def get_date_range_for_week(week: int):
    """取得指定週別的日期範圍"""
    base_date = datetime.strptime('20250202', '%Y%m%d')  # W6 開始日期
    days_to_add = (week - 6) * 7  # 從 W6 開始計算
    start_date = base_date + timedelta(days=days_to_add)
    end_date = start_date + timedelta(days=6)
    return start_date, end_date

def get_current_week() -> int:
    """取得當前週別"""
    today = datetime.now()
    today_str = today.strftime('%Y%m%d')
    return get_week_number(today_str)

# 修正後的解析邏輯
def parse_log_entry(lines):
    """解析單個日誌條目"""
    try:
        # 解析時間戳和類型
        first_line = lines[0].strip()
        timestamp_match = re.match(r'(\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2}:\d{2}\.\d{3})', first_line)
        if not timestamp_match:
            return None
        
        timestamp = datetime.strptime(timestamp_match.group(1), '%Y/%m/%d %H:%M:%S.%f')
        
        # 組合並解析 JSON
        json_text = ''.join(lines[1:])
        data = json.loads(json_text)
        
        return {
            'timestamp': timestamp,
            'data': data,
            'type': 'request' if '[Rqst]' in first_line else 'reply'
        }
    except Exception as e:
        logging.debug(f"解析錯誤: {str(e)}")
        logging.debug(f"問題資料: {lines}")
        return None

def parse_log_file_for_excel(file_path):
    """解析整個日誌檔案 - 用於 Excel 輸出"""
    entries = []
    current_entry = []
    
    try:
        # 一次性讀取整個文件內容
        with open(file_path, 'r', encoding='utf-8-sig') as file:
            lines = file.readlines()
            
        for line in lines:
            line = line.rstrip()
            
            # 檢查是否為新條目的開始
            if re.match(r'\d{4}/\d{2}/\d{2}', line):
                if current_entry:
                    parsed = parse_log_entry(current_entry)
                    if parsed:
                        entries.append(parsed)
                current_entry = [line]
            else:
                if line:  # 只添加非空行
                    current_entry.append(line)
        
        # 處理最後一個條目
        if current_entry:
            parsed = parse_log_entry(current_entry)
            if parsed:
                entries.append(parsed)
    
    except Exception as e:
        logging.error(f"檔案讀取錯誤 {file_path}: {str(e)}")
        return []
        
    return entries

def pair_requests_replies(entries):
    """配對請求和回覆"""
    pairs = []
    request_buffer = {}
    
    for entry in entries:
        if entry['type'] == 'request':
            # 使用 service_name 作為配對的鍵
            service_name = entry['data'].get('service_name')
            if service_name:
                request_buffer[service_name] = entry
        elif entry['type'] == 'reply':
            # 尋找對應的請求
            service_name = entry['data'].get('service_name')
            if service_name and service_name in request_buffer:
                pairs.append({
                    'request': request_buffer.pop(service_name),
                    'reply': entry
                })
    
    # 處理沒有回覆的請求
    for request in request_buffer.values():
        pairs.append({
            'request': request,
            'reply': None
        })
    
    return pairs

def test_single_file_parsing(file_path: Path):
    """測試單個檔案的解析"""
    logging.info(f"測試檔案: {file_path}")
    
    # 解析檔案
    entries = parse_log_file_for_excel(file_path)
    logging.info(f"解析到 {len(entries)} 個條目")
    
    if not entries:
        logging.warning("沒有解析到任何條目")
        return {}
    
    # 配對請求和回覆
    pairs = pair_requests_replies(entries)
    logging.info(f"配對到 {len(pairs)} 個請求-回覆對")
    
    # 按服務名稱分組
    results = {}
    for pair in pairs:
        request = pair['request']
        reply = pair['reply']
        
        service_name = request['data'].get('service_name', 'Unknown')
        
        if service_name not in results:
            results[service_name] = []
            
        record = {
            'Rqst時間': request['timestamp'],
            'Rply時間': reply['timestamp'] if reply else '無回復資料',
            '處理時間(秒)': (reply['timestamp'] - request['timestamp']).total_seconds() if reply else None,
            'service_name': service_name
        }
        
        # 處理請求資料
        request_data = request['data'].get('request_data')
        if isinstance(request_data, dict):
            for key, value in request_data.items():
                record[f'request_data_{key}'] = value
        elif request_data is not None:
            record['request_data'] = request_data
        
        # 處理回覆資料
        if reply:
            reply_data = reply['data'].get('reply_data')
            if isinstance(reply_data, dict):
                for key, value in reply_data.items():
                    record[f'reply_data_{key}'] = value
            elif reply_data is not None:
                record['reply_data'] = reply_data
        
        results[service_name].append(record)
    
    # 輸出統計
    for service_name, records in results.items():
        logging.info(f"服務 {service_name}: {len(records)} 筆記錄")
    
    return results

def test_week_files_parsing():
    """測試週別檔案解析"""
    setup_logging()
    
    current_week = get_current_week()
    test_week = current_week - 1
    start_date, end_date = get_date_range_for_week(test_week)
    
    logging.info(f"測試週別 W{test_week}: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
    
    # 測試第一個資料夾
    test_folder = "DFLM_HKT-53001"
    base_folder = Path(BASE_LOG_PATH) / test_folder
    
    if not base_folder.exists():
        logging.error(f"測試資料夾不存在: {base_folder}")
        return False
    
    # 收集週別檔案
    months_to_check = set()
    current_date = start_date
    while current_date <= end_date:
        months_to_check.add(current_date.strftime('%Y%m'))
        current_date += timedelta(days=1)
    
    all_files = []
    for month in months_to_check:
        month_folder = base_folder / month
        if month_folder.exists():
            month_files = list(month_folder.glob('*.log'))
            all_files.extend(month_files)
            logging.info(f"月份 {month}: 找到 {len(month_files)} 個檔案")
    
    # 過濾週別檔案
    week_files = []
    for f in all_files:
        try:
            file_date_str = f.stem.split('_')[-1]
            file_date = datetime.strptime(file_date_str, '%Y%m%d')
            if start_date <= file_date <= end_date:
                week_files.append(f)
        except (ValueError, IndexError):
            continue
    
    logging.info(f"週別 W{test_week} 符合的檔案數量: {len(week_files)}")
    
    if not week_files:
        logging.warning("沒有找到符合的檔案")
        return False
    
    # 測試前3個檔案
    total_results = {}
    for i, file_path in enumerate(week_files[:3]):
        logging.info(f"\n--- 測試檔案 {i+1}/{min(3, len(week_files))} ---")
        file_results = test_single_file_parsing(file_path)
        
        # 合併結果
        for service_name, records in file_results.items():
            if service_name not in total_results:
                total_results[service_name] = []
            total_results[service_name].extend(records)
    
    # 總結
    logging.info("\n=== 總結 ===")
    total_records = 0
    for service_name, records in total_results.items():
        logging.info(f"服務 {service_name}: {len(records)} 筆記錄")
        total_records += len(records)
    
    logging.info(f"總共解析到 {total_records} 筆記錄")
    
    return total_records > 0

if __name__ == "__main__":
    print("開始測試修正後的 Excel 解析功能...")
    
    success = test_week_files_parsing()
    
    if success:
        print("✅ Excel 解析測試成功！")
    else:
        print("❌ Excel 解析測試失敗！")
    
    print("詳細日誌請查看 test_excel_parsing.log")
