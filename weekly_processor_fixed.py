#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel 資料處理程式
專門處理週別 Excel 資料輸出功能
"""

import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
from typing import List, Tuple, Optional, Dict, Any
import pandas as pd

# 設定日誌
def setup_logging():
    """設定日誌配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('excel_processor.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

# 資料夾列表
AULINK_FOLDERS = [
    'DFLM_HKT-53001', 'DFLM_HKT-53002', 'DFLM_HKT-53003',
    'DV_V_AP-53201', 'DV_V_AP-53202', 'DV_V_AP-53301',
    'LA_NM-53001', 'LA_NM-53002', 'LA_NM-53003', 'LA_NM-53004',
    'MEC_FK-53101', 'MEC_FK-53301', 'SMVC_RH600-53002'
]

# 基礎路徑配置
BASE_LOG_PATH = r"\\k5dcnas02\IME\AAS_LOG"
BASE_OUTPUT_PATH = r"\\k5dcnas02\IME\個人資料夾\VICTOR\對對對對對\MO攔檢率\Rowdata"

# 週別計算函數
def get_week_number(date_str: str) -> int:
    """根據日期取得週別"""
    date = datetime.strptime(date_str, '%Y%m%d')
    base_date = datetime.strptime('20250202', '%Y%m%d')  # W6 開始日期
    days_diff = (date - base_date).days
    week_diff = days_diff // 7
    return week_diff + 6  # 從 W6 開始

def get_date_range_for_week(week: int) -> Tuple[datetime, datetime]:
    """取得指定週別的日期範圍"""
    base_date = datetime.strptime('20250202', '%Y%m%d')  # W6 開始日期
    days_to_add = (week - 6) * 7  # 從 W6 開始計算
    start_date = base_date + timedelta(days=days_to_add)
    end_date = start_date + timedelta(days=6)
    return start_date, end_date

def get_current_week() -> int:
    """取得當前週別"""
    today = datetime.now()
    today_str = today.strftime('%Y%m%d')
    return get_week_number(today_str)

# 檔案收集函數（用於 Excel 處理）

# Excel 處理功能 - 基於 Jupyter notebook 的邏輯
import re

def parse_log_entry(lines):
    """解析單個日誌條目"""
    try:
        # 解析時間戳和類型
        first_line = lines[0].strip()
        timestamp_match = re.match(r'(\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2}:\d{2}\.\d{3})', first_line)
        if not timestamp_match:
            return None

        timestamp = datetime.strptime(timestamp_match.group(1), '%Y/%m/%d %H:%M:%S.%f')

        # 組合並解析 JSON
        json_text = ''.join(lines[1:])
        data = json.loads(json_text)

        return {
            'timestamp': timestamp,
            'data': data,
            'type': 'request' if '[Rqst]' in first_line else 'reply'
        }
    except Exception as e:
        logging.debug(f"解析錯誤: {str(e)}")
        logging.debug(f"問題資料: {lines}")
        return None

def parse_log_file_for_excel(file_path):
    """解析整個日誌檔案 - 用於 Excel 輸出"""
    entries = []
    current_entry = []

    try:
        # 一次性讀取整個文件內容
        with open(file_path, 'r', encoding='utf-8-sig') as file:
            lines = file.readlines()

        for line in lines:
            line = line.rstrip()

            # 檢查是否為新條目的開始
            if re.match(r'\d{4}/\d{2}/\d{2}', line):
                if current_entry:
                    parsed = parse_log_entry(current_entry)
                    if parsed:
                        entries.append(parsed)
                current_entry = [line]
            else:
                if line:  # 只添加非空行
                    current_entry.append(line)

        # 處理最後一個條目
        if current_entry:
            parsed = parse_log_entry(current_entry)
            if parsed:
                entries.append(parsed)

    except Exception as e:
        logging.error(f"檔案讀取錯誤 {file_path}: {str(e)}")
        return []

    return entries

def pair_requests_replies(entries):
    """配對請求和回覆"""
    pairs = []
    request_buffer = {}

    for entry in entries:
        if entry['type'] == 'request':
            # 使用 service_name 作為配對的鍵
            service_name = entry['data'].get('service_name')
            if service_name:
                request_buffer[service_name] = entry
        elif entry['type'] == 'reply':
            # 尋找對應的請求
            service_name = entry['data'].get('service_name')
            if service_name and service_name in request_buffer:
                pairs.append({
                    'request': request_buffer.pop(service_name),
                    'reply': entry
                })

    # 處理沒有回覆的請求
    for request in request_buffer.values():
        pairs.append({
            'request': request,
            'reply': None
        })

    return pairs

def process_single_log_file(file_path: Path) -> Dict[str, List[Dict[str, Any]]]:
    """處理單個日誌檔案 - 使用新的解析邏輯"""
    results = {}

    try:
        # 使用新的解析邏輯
        entries = parse_log_file_for_excel(file_path)
        if not entries:
            return results

        # 配對請求和回覆
        pairs = pair_requests_replies(entries)

        # 按服務名稱分組
        for pair in pairs:
            request = pair['request']
            reply = pair['reply']

            # 使用 service_name 作為分組依據
            service_name = request['data'].get('service_name', 'Unknown')

            if service_name not in results:
                results[service_name] = []

            record = {
                'Rqst時間': request['timestamp'],
                'Rply時間': reply['timestamp'] if reply else '無回復資料',
                '處理時間(秒)': (reply['timestamp'] - request['timestamp']).total_seconds() if reply else None,
                'service_name': service_name
            }

            # 處理請求資料 (request_data)
            request_data = request['data'].get('request_data')
            if isinstance(request_data, dict):
                for key, value in request_data.items():
                    record[f'request_data_{key}'] = value
            elif request_data is not None:
                record['request_data'] = request_data

            # 處理回覆資料 (reply_data)
            if reply:
                reply_data = reply['data'].get('reply_data')
                if isinstance(reply_data, dict):
                    for key, value in reply_data.items():
                        record[f'reply_data_{key}'] = value
                elif reply_data is not None:
                    record['reply_data'] = reply_data

            results[service_name].append(record)

    except Exception as e:
        logging.error(f"處理檔案 {file_path} 時發生錯誤: {e}")

    return results

def collect_week_files(folder: str, week: int) -> List[Path]:
    """收集指定週別的所有檔案"""
    start_date, end_date = get_date_range_for_week(week)
    
    # 獲取所有相關月份
    months_to_check = set()
    current_date = start_date
    while current_date <= end_date:
        months_to_check.add(current_date.strftime('%Y%m'))
        current_date += timedelta(days=1)
    
    # 收集所有相關月份的檔案
    all_files = []
    base_folder = Path(BASE_LOG_PATH) / folder
    
    for month in months_to_check:
        month_folder = base_folder / month
        if month_folder.exists():
            month_files = list(month_folder.glob('*.log'))
            all_files.extend(month_files)
    
    # 過濾出該週的檔案
    week_files = []
    for f in all_files:
        try:
            file_date_str = f.stem.split('_')[-1]
            file_date = datetime.strptime(file_date_str, '%Y%m%d')
            if start_date <= file_date <= end_date:
                week_files.append(f)
        except (ValueError, IndexError):
            continue
    
    return week_files

def process_excel_data_by_week(week: int, folders: Optional[List[str]] = None, max_workers: int = 4) -> None:
    """週別 Excel 資料處理"""
    if folders is None:
        folders = AULINK_FOLDERS

    start_date, end_date = get_date_range_for_week(week)
    logging.info(f"開始處理週別 W{week} Excel 資料 ({start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')})")

    for folder in folders:
        logging.info(f"開始處理週別 W{week}")

        # 設定輸出路徑
        output_folder = Path(BASE_OUTPUT_PATH) / folder
        output_folder.mkdir(parents=True, exist_ok=True)

        year_suffix = start_date.strftime('%y')
        output_file = output_folder / f"{folder}_{year_suffix}_W{week:02d}.xlsx"

        # 收集週別檔案
        week_files = collect_week_files(folder, week)
        if not week_files:
            logging.warning(f"週別 W{week} 沒有找到 {folder} 的檔案")
            continue

        logging.info(f"找到 {len(week_files)} 個檔案")

        # 多線程處理檔案
        all_results = {}
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_file = {executor.submit(process_single_log_file, file_path): file_path
                             for file_path in week_files}

            with tqdm(total=len(week_files), desc="處理檔案") as pbar:
                for future in as_completed(future_to_file):
                    file_results = future.result()

                    # 合併結果
                    for service_name, records in file_results.items():
                        if service_name not in all_results:
                            all_results[service_name] = []
                        all_results[service_name].extend(records)

                    pbar.update(1)

        # 合併處理結果
        logging.info("合併處理結果...")

        # 輸出統計
        for service_name, records in all_results.items():
            logging.info(f"服務 {service_name}: {len(records)} 筆記錄")

        # 寫入 Excel 檔案
        if all_results:
            logging.info(f"寫入 Excel 檔案: {output_file}")

            try:
                with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                    for service_name, records in all_results.items():
                        if records:
                            # 直接使用記錄，因為已經是正確的格式
                            df = pd.DataFrame(records)

                            # 限制工作表名稱長度
                            sheet_name = service_name[:31] if len(service_name) > 31 else service_name
                            df.to_excel(writer, sheet_name=sheet_name, index=False)
                            logging.info(f"工作表 '{sheet_name}' 寫入完成: {len(df)} 筆記錄")

                # 檢查檔案是否成功創建
                if output_file.exists():
                    file_size = output_file.stat().st_size
                    logging.info(f"Excel 檔案創建成功！檔案大小: {file_size} bytes")
                else:
                    logging.error("Excel 檔案創建失敗！")

            except Exception as e:
                logging.error(f"寫入 Excel 檔案時發生錯誤: {e}")
        else:
            logging.warning("沒有資料可寫入 Excel 檔案")

        logging.info(f"完成處理資料夾: {Path(BASE_LOG_PATH) / folder}")

def main():
    """主程式 - 處理上一週的 Excel 資料"""
    setup_logging()

    # 取得當前週別，處理上一週
    current_week = get_current_week()
    process_week = current_week - 1

    start_date, end_date = get_date_range_for_week(process_week)
    logging.info(f"開始處理週別 W{process_week} Excel 資料 ({start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')})")

    try:
        # 處理 Excel 資料輸出
        logging.info("="*60)
        logging.info("開始 Excel 資料處理")
        logging.info("="*60)
        process_excel_data_by_week(process_week)

        logging.info("="*60)
        logging.info(f"週別 W{process_week} Excel 處理完成！")
        logging.info("="*60)

    except Exception as e:
        logging.error(f"主程式執行錯誤: {str(e)}")

def test_functionality():
    """測試 Excel 處理功能"""
    setup_logging()

    # 測試週別計算
    current_week = get_current_week()
    logging.info(f"當前週別: W{current_week}")

    test_week = current_week - 1
    start_date, end_date = get_date_range_for_week(test_week)
    logging.info(f"測試週別 W{test_week}: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")

    # 測試檔案收集（只測試第一個資料夾）
    test_folder = AULINK_FOLDERS[0]
    logging.info(f"測試收集 {test_folder} 的檔案...")

    week_files = collect_week_files(test_folder, test_week)
    logging.info(f"找到 {len(week_files)} 個檔案")

    if week_files:
        logging.info("前5個檔案:")
        for i, file_path in enumerate(week_files[:5]):
            logging.info(f"  {i+1}. {file_path}")

    # 測試輸出路徑
    output_folder = Path(BASE_OUTPUT_PATH) / test_folder
    logging.info(f"輸出路徑: {output_folder}")
    logging.info(f"輸出路徑存在: {output_folder.exists()}")

    # 測試單個資料夾的 Excel 處理
    if week_files:
        logging.info("測試 Excel 處理...")
        process_excel_data_by_week(test_week, [test_folder])

    return len(week_files) > 0

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "test":
            print("執行 Excel 處理測試模式...")
            test_functionality()
        elif sys.argv[1].startswith("week="):
            # 處理特定週別: python weekly_processor_fixed.py week=25
            week_num = int(sys.argv[1].split("=")[1])
            setup_logging()
            logging.info(f"處理指定週別 W{week_num} Excel 資料")
            process_excel_data_by_week(week_num)
            logging.info(f"完成！")
        else:
            print("用法:")
            print("  python weekly_processor_fixed.py          # 處理上一週 Excel 資料")
            print("  python weekly_processor_fixed.py test     # 測試模式")
            print("  python weekly_processor_fixed.py week=25  # 處理指定週別")
    else:
        print("執行 Excel 處理主程式...")
        main()
