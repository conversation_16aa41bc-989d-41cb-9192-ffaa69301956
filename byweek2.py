#!/usr/bin/env python
# coding: utf-8

# In[3]:


import os
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
from datetime import datetime, timedelta
import logging
from typing import List, Dict, Any
import pandas as pd


# In[5]:


aulink_folder = ['DFLM_HKT-53001','DFLM_HKT-53002', 'DFLM_HKT-53003',
 'DV_V_AP-53201', 'DV_V_AP-53202', 'DV_V_AP-53301',
 'LA_NM-53001', 'LA_NM-53002', 'LA_NM-53003',
 'LA_NM-53004', 'MEC_FK-53101', 'MEC_FK-53301', 'SMVC_RH600-53002']  


# In[1]:


# 設定日誌
def setup_logging():
    """設定日誌配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('text_replacement_processor.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

# 資料夾列表
AULINK_FOLDERS = [
    'DFLM_HKT-53001', 'DFLM_HKT-53002', 'DFLM_HKT-53003',
    'DV_V_AP-53201', 'DV_V_AP-53202', 'DV_V_AP-53301',
    'LA_NM-53001', 'LA_NM-53002', 'LA_NM-53003', 'LA_NM-53004',
    'MEC_FK-53101', 'MEC_FK-53301', 'SMVC_RH600-53002'
]

# 基礎路徑配置
BASE_LOG_PATH = r"\\k5dcnas02\IME\AAS_LOG"

# 預設替換規則
DEFAULT_REPLACEMENTS = [
    ('請按下""警報復歸""鈕', "請按下(警報復歸)鈕"),
    ('請按下警報復歸"鈕"', "請按下(警報復歸)鈕"), 
    ('"請按下""警報復歸""鈕"', "請按下(警報復歸)鈕"),
    ("請按下\"警報復歸\"鈕", "請按下(警報復歸)鈕"),
    ('"請按下(警報復歸)鈕"', "請按下(警報復歸)鈕")
]

# 週別計算函數
def get_week_number(date_str: str) -> int:
    """根據日期取得週別"""
    date = datetime.strptime(date_str, '%Y%m%d')
    base_date = datetime.strptime('20250202', '%Y%m%d')  # W6 開始日期
    days_diff = (date - base_date).days
    week_diff = days_diff // 7
    return week_diff + 6  # 從 W6 開始

def get_date_range_for_week(week: int) -> Tuple[datetime, datetime]:
    """取得指定週別的日期範圍"""
    base_date = datetime.strptime('20250202', '%Y%m%d')  # W6 開始日期
    days_to_add = (week - 6) * 7  # 從 W6 開始計算
    start_date = base_date + timedelta(days=days_to_add)
    end_date = start_date + timedelta(days=6)
    return start_date, end_date

def get_current_week() -> int:
    """取得當前週別"""
    today = datetime.now()
    today_str = today.strftime('%Y%m%d')
    return get_week_number(today_str)

# 檔案收集函數
def collect_week_files(folder: str, week: int) -> List[Path]:
    """收集指定週別的所有檔案"""
    start_date, end_date = get_date_range_for_week(week)
    
    # 獲取所有相關月份
    months_to_check = set()
    current_date = start_date
    while current_date <= end_date:
        months_to_check.add(current_date.strftime('%Y%m'))
        current_date += timedelta(days=1)
    
    logging.info(f"資料夾 {folder} 需要檢查的月份: {sorted(months_to_check)}")
    
    # 收集所有相關月份的檔案
    all_files = []
    base_folder = Path(BASE_LOG_PATH) / folder
    
    for month in months_to_check:
        month_folder = base_folder / month
        if month_folder.exists():
            month_files = list(month_folder.glob('*.log'))
            all_files.extend(month_files)
            logging.info(f"  月份 {month}: 找到 {len(month_files)} 個檔案")
        else:
            logging.warning(f"  月份 {month}: 資料夾不存在")
    
    # 過濾出該週的檔案
    week_files = []
    for f in all_files:
        try:
            file_date_str = f.stem.split('_')[-1]
            file_date = datetime.strptime(file_date_str, '%Y%m%d')
            if start_date <= file_date <= end_date:
                week_files.append(f)
        except (ValueError, IndexError) as e:
            logging.warning(f"無法解析檔案日期: {f.name}, 錯誤: {e}")
            continue
    
    logging.info(f"週別 W{week} 符合的檔案數量: {len(week_files)}")
    return week_files

# 文字替換功能
def process_single_file(file_path: Path, replacements: List[Tuple[str, str]]) -> Tuple[bool, Optional[Tuple[str, str]]]:
    """處理單個檔案的文字替換"""
    try:
        # 讀取檔案內容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 一次性進行所有替換
        new_content = content
        for old, new in replacements:
            new_content = new_content.replace(old, new)
            
        # 只有在內容有變化時才寫入
        if new_content != content:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(new_content)
                
        return True, None
        
    except Exception as e:
        return False, (str(file_path), str(e))

def process_text_replacement_by_week(week: int, replacements: List[Tuple[str, str]], folders: Optional[List[str]] = None) -> Tuple[int, List[Tuple[str, str]]]:
    """週別文字替換處理"""
    if folders is None:
        folders = AULINK_FOLDERS
    
    start_date, end_date = get_date_range_for_week(week)
    logging.info(f"開始處理週別 W{week} 文字替換 ({start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')})")
    
    total_processed = 0
    total_failures = []
    
    for folder in folders:
        logging.info(f"處理資料夾: {folder}")
        
        week_files = collect_week_files(folder, week)
        if not week_files:
            logging.warning(f"週別 W{week} 沒有找到 {folder} 的檔案")
            continue
        
        folder_processed = 0
        folder_failures = []
        
        for file_path in week_files:
            success, result = process_single_file(file_path, replacements)
            if success:
                folder_processed += 1
            else:
                folder_failures.append(result)
        
        logging.info(f"{folder} 處理完成: 成功 {folder_processed} 個檔案")
        if folder_failures:
            logging.warning(f"{folder} 處理失敗: {len(folder_failures)} 個檔案")
            for file_path, error in folder_failures:
                logging.warning(f"  失敗檔案: {file_path}, 錯誤: {error}")
        
        total_processed += folder_processed
        total_failures.extend(folder_failures)
    
    logging.info(f"週別 W{week} 文字替換完成: 總共處理 {total_processed} 個檔案")
    return total_processed, total_failures

def process_multiple_weeks_text(start_week: int, end_week: int, replacements: List[Tuple[str, str]], folders: Optional[List[str]] = None) -> None:
    """處理多個週別的文字替換"""
    for week in range(start_week, end_week + 1):
        logging.info(f"\n{'='*50}")
        logging.info(f"開始處理週別 W{week} 文字替換")
        logging.info(f"{'='*50}")
        process_text_replacement_by_week(week, replacements, folders)

def main():
    """主程式 - 處理上一週的文字替換"""
    setup_logging()
    
    # 取得當前週別，處理上一週
    current_week = get_current_week()
    process_week = current_week - 1
    
    start_date, end_date = get_date_range_for_week(process_week)
    logging.info(f"開始處理週別 W{process_week} 文字替換 ({start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')})")
    
    try:
        # 處理文字替換
        logging.info("="*60)
        logging.info("開始文字替換處理")
        logging.info("="*60)
        total_processed, failures = process_text_replacement_by_week(process_week, DEFAULT_REPLACEMENTS)
        
        if failures:
            logging.warning(f"文字替換有 {len(failures)} 個檔案處理失敗")
            for file_path, error in failures:
                logging.warning(f"失敗檔案: {file_path}, 錯誤: {error}")
        
        logging.info("="*60)
        logging.info(f"週別 W{process_week} 文字替換完成！")
        logging.info(f"文字替換處理了 {total_processed} 個檔案")
        logging.info("="*60)
        
    except Exception as e:
        logging.error(f"主程式執行錯誤: {str(e)}")

def test_functionality():
    """測試功能"""
    setup_logging()
    
    # 測試週別計算
    current_week = get_current_week()
    logging.info(f"當前週別: W{current_week}")
    
    test_week = current_week - 1
    start_date, end_date = get_date_range_for_week(test_week)
    logging.info(f"測試週別 W{test_week}: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
    
    # 測試檔案收集（只測試第一個資料夾）
    test_folder = AULINK_FOLDERS[0]
    logging.info(f"測試收集 {test_folder} 的檔案...")
    
    week_files = collect_week_files(test_folder, test_week)
    logging.info(f"找到 {len(week_files)} 個檔案")
    
    if week_files:
        logging.info("前5個檔案:")
        for i, file_path in enumerate(week_files[:5]):
            logging.info(f"  {i+1}. {file_path}")
    
    # 測試單個資料夾的文字替換
    if week_files:
        logging.info("測試文字替換...")
        total_processed, failures = process_text_replacement_by_week(test_week, DEFAULT_REPLACEMENTS, [test_folder])
        logging.info(f"測試結果: 處理了 {total_processed} 個檔案")
    
    return len(week_files) > 0

if __name__ == "__main__":
    # import sys
    
    # if len(sys.argv) > 1:
    #     if sys.argv[1] == "test":
    #         print("執行測試模式...")
    #         test_functionality()
    #     elif sys.argv[1].startswith("week="):
    #         # 處理特定週別: python text_replacement_processor.py week=25
    #         week_num = int(sys.argv[1].split("=")[1])
    #         setup_logging()
    #         logging.info(f"處理指定週別 W{week_num}")
    #         total_processed, failures = process_text_replacement_by_week(week_num, DEFAULT_REPLACEMENTS)
    #         logging.info(f"完成！處理了 {total_processed} 個檔案")
    #     else:
    #         print("用法:")
    #         print("  python text_replacement_processor.py          # 處理上一週")
    #         print("  python text_replacement_processor.py test     # 測試模式")
    #         print("  python text_replacement_processor.py week=25  # 處理指定週別")
    # else:
    print("執行主程式...")
    main()


# In[6]:


def get_week_number(date_str):
    """根據日期取得週別"""
    date = datetime.strptime(date_str, '%Y%m%d')
    base_date = datetime.strptime('20250202', '%Y%m%d')  # W6 開始日期
    days_diff = (date - base_date).days
    week_diff = days_diff // 7
    return week_diff + 6  # 從 W6 開始

def get_date_range_for_week(week):
    """取得指定週別的日期範圍"""
    base_date = datetime.strptime('20250202', '%Y%m%d')  # W6 開始日期
    days_to_add = (week - 6) * 7  # 從 W6 開始計算
    start_date = base_date + timedelta(days=days_to_add)
    end_date = start_date + timedelta(days=6)
    return start_date, end_date
import json
from datetime import datetime
import pandas as pd
import re

def setup_logging():
    """設定日誌"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('log_processor.log'),
            logging.StreamHandler()
        ]
    )

def process_single_file_excel(file_path: Path) -> Dict[str, List[Dict[str, Any]]]:
    """處理單個檔案"""
    try:
        entries = parse_log_file(str(file_path))
        if not entries:
            logging.warning(f"檔案無有效記錄: {file_path}")
            return {}
        
        return process_entries(entries)
    except Exception as e:
        logging.error(f"處理檔案時發生錯誤 {file_path}: {str(e)}")
        return {}

def merge_results(results: List[Dict[str, List[Dict[str, Any]]]]) -> Dict[str, List[Dict[str, Any]]]:
    """合併處理結果"""
    merged = {}
    for result in results:
        for service_name, records in result.items():
            if service_name not in merged:
                merged[service_name] = []
            merged[service_name].extend(records)
    
    # 排序每個服務的記錄
    for service_name in merged:
        merged[service_name].sort(key=lambda x: x['Rqst時間'])
    
    return merged

def process_folder(folder_path: str, week: int, max_workers: int = 4) -> None:
    """處理資料夾中的所有檔案"""
    try:
        # 設定路徑
        folder = Path(folder_path)

        output_folder = Path(r"\\k5dcnas02\IME\個人資料夾\VICTOR\對對對對對\MO攔檢率\Rowdata")/folder.name  # 在當前路徑建立 byweek 資料夾
        output_folder.mkdir(parents=True, exist_ok=True)
        
        start_date, _ = get_date_range_for_week(week)
        year_suffix = start_date.strftime('%y')
        output_file = output_folder / (folder.name + f"_{year_suffix}_W{week}.xlsx")
        
        # 取得週別的日期範圍
        start_date, end_date = get_date_range_for_week(week)
        
        # 獲取所有相關月份的檔案
        all_files = []
        months_to_check = set()
        current_date = start_date
        while current_date <= end_date:
            months_to_check.add(current_date.strftime('%Y%m'))
            current_date += timedelta(days=1)
        
        # 收集所有相關月份的檔案
        for month in months_to_check:
            month_folder = folder.parent  / folder.name/ month
            if month_folder.exists():
                all_files.extend(list(month_folder.glob('*.log')))
        
        if not all_files:
            logging.warning(f"找不到週別 W{week} 的相關檔案")
            return
        
        # 過濾出該週的檔案
        files = [f for f in all_files if start_date <= datetime.strptime(f.stem.split('_')[-1], '%Y%m%d') <= end_date]
        for file in files:
            if not files:
                logging.warning(f"週別 W{week} 沒有符合的檔案")
                return
        
        logging.info(f"開始處理週別 W{week}")
        logging.info(f"找到 {len(files)} 個檔案")
        
        # 使用線程池處理檔案
        results = []
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 建立任務
            future_to_file = {
                executor.submit(process_single_file_excel, file): file 
                for file in files
            }
            
            # 使用 tqdm 顯示進度
            with tqdm(total=len(files), desc="處理檔案") as pbar:
                for future in as_completed(future_to_file):
                    file = future_to_file[future]
                    try:
                        result = future.result()
                        if result:
                            results.append(result)
                    except Exception as e:
                        logging.error(f"處理檔案失敗 {file}: {str(e)}")
                    pbar.update(1)
        
        # 合併結果
        if not results:
            logging.warning(f"沒有可用的處理結果: {folder_path}")
            return
            
        logging.info("合併處理結果...")
        merged_data = merge_results(results)
        
        # 顯示統計資訊
        for service_name, records in merged_data.items():
            logging.info(f"服務 {service_name}: {len(records)} 筆記錄")
        
        # 寫入 Excel
        logging.info(f"寫入 Excel 檔案: {output_file}")
        create_excel(merged_data, output_file)
        
        logging.info(f"完成處理資料夾: {folder_path}")
        
    except Exception as e:
        logging.error(f"處理資料夾時發生錯誤 {folder_path}: {str(e)}")


def parse_log_entry(lines):
    """解析單個日誌條目"""
    try:
        # 解析時間戳和類型
        first_line = lines[0].strip()
        timestamp_match = re.match(r'(\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2}:\d{2}\.\d{3})', first_line)
        if not timestamp_match:
            return None
        
        timestamp = datetime.strptime(timestamp_match.group(1), '%Y/%m/%d %H:%M:%S.%f')
        
        # 組合並解析 JSON
        json_text = ''.join(lines[1:])
        data = json.loads(json_text)
        
        return {
            'timestamp': timestamp,
            'data': data,
            'type': 'request' if '[Rqst]' in first_line else 'reply'
        }
    except Exception as e:
        print(f"解析錯誤: {str(e)}")
        print(f"問題資料: {lines}")
        return None

def parse_log_file(file_path):
    """解析整個日誌檔案"""
    entries = []
    current_entry = []
    
    try:
        # 一次性讀取整個文件內容
        with open(file_path, 'r', encoding='utf-8-sig') as file:
            lines = file.readlines()
            
        for line in lines:
            line = line.rstrip()
            
            # 檢查是否為新條目的開始
            if re.match(r'\d{4}/\d{2}/\d{2}', line):
                if current_entry:
                    parsed = parse_log_entry(current_entry)
                    if parsed:
                        entries.append(parsed)
                current_entry = [line]
            else:
                if line:  # 只添加非空行
                    current_entry.append(line)
        
        # 處理最後一個條目
        if current_entry:
            parsed = parse_log_entry(current_entry)
            if parsed:
                entries.append(parsed)
    
    except Exception as e:
        logging.error(f"檔案讀取錯誤 {file_path}: {str(e)}")
        return []
        
    return entries

def pair_requests_replies(entries):
    """配對請求和回覆
    
    配對規則：
    1. 回覆必須在請求之後
    2. 如果一個請求後立即跟著一個回覆，則視為配對
    3. 如果一個請求後面是另一個請求，則第一個請求視為無回覆
    4. 一個回覆只能配對一個請求
    """
    pairs = []
    
    # 為了更容易處理，先把條目按時間排序
    entries = sorted(entries, key=lambda x: x['timestamp'])
    
    # 找出所有請求的索引
    request_indices = [i for i, e in enumerate(entries) if e['type'] == 'request']
    
    for i, req_idx in enumerate(request_indices):
        request = entries[req_idx]
        matching_reply = None
        
        # 檢查下一個條目
        if req_idx + 1 < len(entries):
            next_entry = entries[req_idx + 1]
            
            # 如果下一個條目是回覆，則配對
            if next_entry['type'] == 'reply' and next_entry['data']['service_name'] == request['data']['service_name']:
                matching_reply = next_entry
            # 如果下一個條目是請求，則當前請求視為無回覆
            elif next_entry['type'] == 'request':
                matching_reply = None
        
        # 建立配對記錄
        pairs.append({
            'request': request,
            'reply': matching_reply
        })
        
        # 打印配對結果（用於調試）
        # print(f"\n配對結果:")
        # print(f"Rqst時間: {request['timestamp']}")
        # print(f"請求類型: {request['data']['service_name']}")
        # if matching_reply:
        #     print(f"Rply時間: {matching_reply['timestamp']}")
        #     print(f"回覆類型: {matching_reply['data']['service_name']}")
        # else:
        #     print("無匹配回覆")
    
    return pairs

def process_b_field(b_data, record, prefix='Rqst_'):
    """處理 B 欄位中的數據，包括嵌套的字典和陣列
    
    Args:
        b_data: B 欄位的數據
        record: 要更新的記錄字典
        prefix: 欄位名稱的前綴
    """
    for key, value in b_data.items():
        if isinstance(value, list) and value and isinstance(value[0], dict):
            # 處理字典陣列
            for i, item in enumerate(value):
                for k, v in item.items():
                    # 使用格式化的欄位名稱：prefix_原始key_陣列索引_字典key
                    column_name = f"{prefix}{key}_{i+1}_{k}"
                    record[column_name] = v
        else:
            # 處理一般值
            record[f"{prefix}{key}"] = value

def process_entries(entries):
    """處理解析後的條目"""
    # 配對請求和回覆
    pairs = pair_requests_replies(entries)
    
    # 按 A 類型分組
    grouped_data = {}
    
    for pair in pairs:
        request = pair['request']
        reply = pair['reply']
        
        # 使用 A 作為分組依據
        a_type = request['data']['service_name']
        
        if a_type not in grouped_data:
            grouped_data[a_type] = []
            
        record = {
            'Rqst時間': request['timestamp'],
            'Rply時間': reply['timestamp'] if reply else '無回復資料',
            '處理時間(秒)': (reply['timestamp'] - request['timestamp']).total_seconds() if reply else None,
            'service_name': a_type
        }
        
        # 處理請求資料 (request_data)
        request_data = request['data'].get('request_data')
        if isinstance(request_data, dict):
            for key, value in request_data.items():
                record[f'request_data_{key}'] = value
        elif request_data is not None:
            record['request_data'] = request_data
        
        # 處理請求資料 (B 欄位)
        if 'request_data' in request['data']:
            process_b_field(request['data']['request_data'], record)
        
        # 處理回覆資料
        if reply:
            for key, value in reply['data'].items():
                if key != 'service_name':
                    record[f'Rply_{key}'] = value
        else:
            # 如果沒有回覆，填入空值
            if request['data'].get('request_data'):
                for key in request['data']['request_data'].keys():
                    if not isinstance(request['data']['request_data'][key], list):
                        record[f'Rply_{key}'] = None
        
        grouped_data[a_type].append(record)
    
    return grouped_data

def create_excel(grouped_data, output_path):
    """創建 Excel 檔案"""
    try:
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            for a_type, records in grouped_data.items():
                if not records:
                    continue
                
                # 確保所有記錄有相同的欄位
                all_keys = set()
                for record in records:
                    all_keys.update(record.keys())
                
                # 填充缺失的欄位
                processed_records = []
                for record in records:
                    processed_record = {key: record.get(key, None) for key in all_keys}
                    processed_records.append(processed_record)
                
                # 創建 DataFrame
                df = pd.DataFrame(processed_records)
                
                # 重新排列欄位順序
                timestamp_cols = ['Rqst時間', 'Rply時間', '處理時間(秒)', 'service_name']
                request_data_cols = sorted([col for col in df.columns if col.startswith('request_data_')])
                request_cols = sorted([col for col in df.columns if col.startswith('Rqst_')])
                reply_cols = sorted([col for col in df.columns if col.startswith('Rply_')])
                other_cols = sorted([col for col in df.columns 
                                   if col not in timestamp_cols 
                                   and col not in request_data_cols
                                   and col not in request_cols 
                                   and col not in reply_cols
                                   and col != 'request_data'])
                
                # 重新排序欄位
                cols_order = (timestamp_cols + 
                            (['request_data'] if 'request_data' in df.columns else []) +
                            request_data_cols + 
                            request_cols + 
                            reply_cols + 
                            other_cols)
                
                # 只選擇存在的欄位
                cols_order = [col for col in cols_order if col in df.columns]
                df = df[cols_order]
                
                # 使用 A 類型作為工作表名稱，確保名稱有效
                sheet_name = f'Type_{a_type}'
                sheet_name = re.sub(r'[\[\]:*?/\\]', '_', sheet_name)  # 移除非法字符
                sheet_name = sheet_name[:31]  # Excel工作表名稱限制
                
                # 寫入工作表
                df.to_excel(writer, sheet_name=sheet_name, index=False)
                
    except Exception as e:
        logging.error(f"創建 Excel 時發生錯誤: {str(e)}")
        raise

def process_log_file(input_path, output_path):
    """主要處理函數"""
    print("開始處理日誌檔案...")
    
    # 解析日誌檔案
    entries = parse_log_file(input_path)
    if not entries:
        print("未找到有效的記錄")
        return
    
    # 處理記錄
    grouped_data = process_entries(entries)
    if not grouped_data:
        print("無法處理記錄")
        return
    
    # 創建 Excel 檔案
    print("\n正在產生 Excel 檔案...")
    create_excel(grouped_data, output_path)
    
    print(f"\n處理完成，結果已儲存至 {output_path}")


import re
import pandas as pd
from datetime import datetime

def parse_log_entry3(log_lines):
    data = {
        'timestamp': None,
        'folder': None,
        'file_count': None,
        'services': {}
    }
    
    for line in log_lines:
        match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) - INFO - (.*)', line)
        if not match:
            continue
            
        timestamp, message = match.groups()
        
        folder_match = re.match(r'開始處理資料夾: (.+)', message)
        if folder_match:
            # 修改：只取得倒數第二個路徑部分
            folder_parts = folder_match.group(1).split('\\')
            data['folder'] = folder_parts[-2] if len(folder_parts) >= 2 else folder_match.group(1)
            data['timestamp'] = timestamp
            continue
            
        file_count_match = re.match(r'找到 (\d+) 個檔案', message)
        if file_count_match:
            data['file_count'] = int(file_count_match.group(1))
            continue
            
        # 解析服務記錄數量
        service_match = re.match(r'服務 (.+): (\d+) 筆記錄', message)
        if service_match:
            service_name, count = service_match.groups()
            data['services'][service_name] = int(count)
            
    return data

def process_log_file3(log_file_path):
    entries = []
    current_entry = []
    
    with open(log_file_path, 'r', encoding='big5') as f:
        lines = f.readlines()
        
    for line in lines:
        if '開始處理資料夾' in line:
            if current_entry:
                entry_data = parse_log_entry3(current_entry)
                if entry_data['timestamp']:  # 確保有有效數據
                    entries.append(entry_data)
            current_entry = [line]
        else:
            current_entry.append(line)
    
    # 處理最後一個entry
    if current_entry:
        entry_data = parse_log_entry3(current_entry)
        if entry_data['timestamp']:
            entries.append(entry_data)
    
    return entries

def create_excel3(entries, output_file):
    # 獲取所有可能的服務名稱
    all_services = set()
    for entry in entries:
        all_services.update(entry['services'].keys())
    
    # 準備DataFrame數據
    data = []
    for entry in entries:
        row = {
            '處理時間': entry['timestamp'],
            '資料夾名稱': entry['folder'],
            '檔案數量': entry['file_count']
        }
        # 添加各服務的記錄數量
        for service in all_services:
            row[service] = entry['services'].get(service, 0)
        data.append(row)
    
    # 創建DataFrame並寫入Excel
    df = pd.DataFrame(data)
    df.to_excel(output_file, index=False)
    print("DONE!!!!")

# # 使用示例
def main():
    """主程式"""
    setup_logging()
    
    # 設定處理參數
    max_workers = 4  # 最多使用 4 個線程
    today = datetime.now()
    today_str = today.strftime('%Y%m%d')
    process_week = get_week_number(today_str)-1
    try:
        # 處理每個資料夾
        for folder in aulink_folder:
            base_folder = fr"\\k5dcnas02\IME\AAS_LOG\{folder}"
            # for process_week in range(17,25):
            process_folder(str(base_folder), process_week)
            
    except Exception as e:
        logging.error(f"主程式執行錯誤: {str(e)}")

if __name__ == "__main__":
    main()

