#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自動打包 byweek2.py 為 EXE 檔案
包含自動套件安裝和依賴檢測
"""

import subprocess
import sys
import os
from pathlib import Path
import importlib.util

def check_and_install_package(package_name, import_name=None):
    """檢查並安裝套件"""
    if import_name is None:
        import_name = package_name
    
    try:
        # 嘗試導入套件
        spec = importlib.util.find_spec(import_name)
        if spec is not None:
            print(f"✅ {package_name} 已安裝")
            return True
    except ImportError:
        pass
    
    print(f"❌ {package_name} 未安裝，開始安裝...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ {package_name} 安裝成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安裝失敗: {e}")
        return False

def install_required_packages():
    """安裝所需套件"""
    print("檢查並安裝所需套件...")
    
    # 需要的套件列表 (套件名稱, 導入名稱)
    required_packages = [
        ("pandas", "pandas"),
        ("tqdm", "tqdm"),
        ("openpyxl", "openpyxl"),
        ("pyinstaller", "PyInstaller")
    ]
    
    all_installed = True
    for package_name, import_name in required_packages:
        if not check_and_install_package(package_name, import_name):
            all_installed = False
    
    return all_installed

def create_spec_file():
    """創建 PyInstaller spec 檔案"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['byweek2.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'pandas',
        'tqdm',
        'openpyxl',
        'concurrent.futures',
        'pathlib',
        'datetime',
        'logging',
        'typing',
        'json',
        're',
        'os'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='byweek2_processor',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('byweek2.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 已創建 byweek2.spec 檔案")

def build_exe():
    """使用 PyInstaller 建立 EXE"""
    print("開始建立 EXE 檔案...")
    
    try:
        # 使用 spec 檔案建立
        result = subprocess.run([
            sys.executable, "-m", "PyInstaller", 
            "--clean",
            "byweek2.spec"
        ], capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ EXE 檔案建立成功！")
            print(f"輸出位置: {os.path.abspath('dist/byweek2_processor.exe')}")
            return True
        else:
            print("❌ EXE 檔案建立失敗")
            print("錯誤輸出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 建立過程發生錯誤: {e}")
        return False

def create_batch_file():
    """創建批次檔案方便執行"""
    batch_content = '''@echo off
chcp 65001 > nul
echo 週別處理程式
echo ================
echo.
echo 開始執行週別處理...
echo.

byweek2_processor.exe

echo.
echo 處理完成！
echo 按任意鍵關閉視窗...
pause > nul
'''
    
    with open('dist/run_byweek2.bat', 'w', encoding='utf-8') as f:
        f.write(batch_content)
    
    print("✅ 已創建執行批次檔案: dist/run_byweek2.bat")

def create_readme():
    """創建說明檔案"""
    readme_content = '''# 週別處理程式 EXE 版本

## 檔案說明

- `byweek2_processor.exe` - 主程式執行檔
- `run_byweek2.bat` - 執行批次檔（推薦使用）
- `README.txt` - 本說明檔案

## 使用方式

### 方法一：使用批次檔（推薦）
雙擊 `run_byweek2.bat` 執行

### 方法二：直接執行
雙擊 `byweek2_processor.exe` 執行

## 功能說明

程式會自動：
1. 計算當前週別
2. 處理上一週（當前週別-1）的資料
3. 執行文字替換處理
4. 生成 Excel 資料檔案

## 輸出位置

- 日誌檔案：程式執行目錄下的 `byweek2_processor.log`
- Excel 檔案：`\\\\k5dcnas02\\IME\\個人資料夾\\VICTOR\\對對對對對\\MO攔檢率\\Rowdata\\`

## 處理的資料夾

- DFLM_HKT-53001, DFLM_HKT-53002, DFLM_HKT-53003
- DV_V_AP-53201, DV_V_AP-53202, DV_V_AP-53301
- LA_NM-53001, LA_NM-53002, LA_NM-53003, LA_NM-53004
- MEC_FK-53101, MEC_FK-53301, SMVC_RH600-53002

## 注意事項

1. 確保可以存取網路路徑 `\\\\k5dcnas02\\IME\\`
2. 確保有讀寫 .log 檔案的權限
3. 程式執行時會顯示處理進度
4. 完成後請檢查日誌檔案確認處理結果

## 故障排除

如果程式無法正常執行：
1. 檢查網路連線和路徑權限
2. 查看日誌檔案了解錯誤詳情
3. 確認日期和週別計算是否正確

---
建立時間：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
'''
    
    from datetime import datetime
    
    with open('dist/README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 已創建說明檔案: dist/README.txt")

def main():
    """主程式"""
    print("=" * 60)
    print("週別處理程式 EXE 打包工具")
    print("=" * 60)
    
    # 檢查 byweek2.py 是否存在
    if not Path("byweek2.py").exists():
        print("❌ 找不到 byweek2.py 檔案")
        print("請確保 byweek2.py 在當前目錄中")
        return False
    
    print("✅ 找到 byweek2.py 檔案")
    
    # 1. 安裝所需套件
    print("\n" + "=" * 40)
    print("步驟 1: 檢查並安裝所需套件")
    print("=" * 40)
    
    if not install_required_packages():
        print("❌ 套件安裝失敗，無法繼續")
        return False
    
    # 2. 創建 spec 檔案
    print("\n" + "=" * 40)
    print("步驟 2: 創建 PyInstaller 配置檔案")
    print("=" * 40)
    
    create_spec_file()
    
    # 3. 建立 EXE
    print("\n" + "=" * 40)
    print("步驟 3: 建立 EXE 檔案")
    print("=" * 40)
    
    if not build_exe():
        print("❌ EXE 建立失敗")
        return False
    
    # 4. 創建輔助檔案
    print("\n" + "=" * 40)
    print("步驟 4: 創建輔助檔案")
    print("=" * 40)
    
    create_batch_file()
    create_readme()
    
    # 完成
    print("\n" + "=" * 60)
    print("🎉 EXE 打包完成！")
    print("=" * 60)
    print(f"📁 輸出目錄: {os.path.abspath('dist')}")
    print("📋 包含檔案:")
    print("   - byweek2_processor.exe (主程式)")
    print("   - run_byweek2.bat (執行批次檔)")
    print("   - README.txt (使用說明)")
    print("\n💡 建議使用 run_byweek2.bat 來執行程式")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ 打包過程中發生錯誤")
            input("按 Enter 鍵退出...")
        else:
            print("\n✅ 打包成功完成")
            input("按 Enter 鍵退出...")
    except KeyboardInterrupt:
        print("\n\n⚠️  用戶中斷操作")
    except Exception as e:
        print(f"\n❌ 發生未預期的錯誤: {e}")
        input("按 Enter 鍵退出...")
