#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試週別處理程式
"""

import json
import logging
from datetime import datetime, timedelta
from pathlib import Path

# 設定日誌
def setup_logging():
    """設定日誌配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('test_weekly_processor.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

# 基礎路徑配置
BASE_LOG_PATH = r"\\k5dcnas02\IME\AAS_LOG"
BASE_OUTPUT_PATH = r"\\k5dcnas02\IME\個人資料夾\VICTOR\對對對對對\MO攔檢率\Rowdata"

# 資料夾列表
AULINK_FOLDERS = [
    'DFLM_HKT-53001', 'DFLM_HKT-53002', 'DFLM_HKT-53003',
    'DV_V_AP-53201', 'DV_V_AP-53202', 'DV_V_AP-53301',
    'LA_NM-53001', 'LA_NM-53002', 'LA_NM-53003', 'LA_NM-53004',
    'MEC_FK-53101', 'MEC_FK-53301', 'SMVC_RH600-53002'
]

# 週別計算函數
def get_week_number(date_str: str) -> int:
    """根據日期取得週別"""
    date = datetime.strptime(date_str, '%Y%m%d')
    base_date = datetime.strptime('20250202', '%Y%m%d')  # W6 開始日期
    days_diff = (date - base_date).days
    week_diff = days_diff // 7
    return week_diff + 6  # 從 W6 開始

def get_date_range_for_week(week: int):
    """取得指定週別的日期範圍"""
    base_date = datetime.strptime('20250202', '%Y%m%d')  # W6 開始日期
    days_to_add = (week - 6) * 7  # 從 W6 開始計算
    start_date = base_date + timedelta(days=days_to_add)
    end_date = start_date + timedelta(days=6)
    return start_date, end_date

def get_current_week() -> int:
    """取得當前週別"""
    today = datetime.now()
    today_str = today.strftime('%Y%m%d')
    return get_week_number(today_str)

def test_basic_functions():
    """測試基本功能"""
    setup_logging()
    
    logging.info("開始測試基本功能...")
    
    # 測試週別計算
    current_week = get_current_week()
    logging.info(f"當前週別: W{current_week}")
    
    process_week = current_week - 1
    start_date, end_date = get_date_range_for_week(process_week)
    logging.info(f"處理週別 W{process_week}: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
    
    # 測試路徑
    base_log_path = Path(BASE_LOG_PATH)
    base_output_path = Path(BASE_OUTPUT_PATH)
    
    logging.info(f"日誌基礎路徑: {base_log_path}")
    logging.info(f"日誌路徑存在: {base_log_path.exists()}")
    
    logging.info(f"輸出基礎路徑: {base_output_path}")
    logging.info(f"輸出路徑存在: {base_output_path.exists()}")
    
    # 測試第一個資料夾
    test_folder = AULINK_FOLDERS[0]
    test_folder_path = base_log_path / test_folder
    logging.info(f"測試資料夾: {test_folder}")
    logging.info(f"測試資料夾路徑: {test_folder_path}")
    logging.info(f"測試資料夾存在: {test_folder_path.exists()}")
    
    if test_folder_path.exists():
        # 列出子資料夾（月份）
        subdirs = [d for d in test_folder_path.iterdir() if d.is_dir()]
        logging.info(f"找到 {len(subdirs)} 個子資料夾:")
        for subdir in sorted(subdirs)[:5]:  # 只顯示前5個
            logging.info(f"  {subdir.name}")
    
    # 測試輸出資料夾
    output_folder = base_output_path / test_folder
    logging.info(f"輸出資料夾: {output_folder}")
    logging.info(f"輸出資料夾存在: {output_folder.exists()}")
    
    # 嘗試創建輸出資料夾
    try:
        output_folder.mkdir(parents=True, exist_ok=True)
        logging.info("輸出資料夾創建成功")
    except Exception as e:
        logging.error(f"創建輸出資料夾失敗: {e}")
    
    logging.info("基本功能測試完成")

def test_file_collection():
    """測試檔案收集功能"""
    setup_logging()
    
    current_week = get_current_week()
    process_week = current_week - 1
    
    logging.info(f"測試收集週別 W{process_week} 的檔案...")
    
    # 測試第一個資料夾
    test_folder = AULINK_FOLDERS[0]
    base_folder = Path(BASE_LOG_PATH) / test_folder
    
    if not base_folder.exists():
        logging.error(f"測試資料夾不存在: {base_folder}")
        return
    
    start_date, end_date = get_date_range_for_week(process_week)
    
    # 獲取所有相關月份
    months_to_check = set()
    current_date = start_date
    while current_date <= end_date:
        months_to_check.add(current_date.strftime('%Y%m'))
        current_date += timedelta(days=1)
    
    logging.info(f"需要檢查的月份: {sorted(months_to_check)}")
    
    # 收集檔案
    all_files = []
    for month in months_to_check:
        month_folder = base_folder / month
        if month_folder.exists():
            month_files = list(month_folder.glob('*.log'))
            all_files.extend(month_files)
            logging.info(f"月份 {month}: 找到 {len(month_files)} 個檔案")
        else:
            logging.warning(f"月份 {month}: 資料夾不存在")
    
    # 過濾出該週的檔案
    week_files = []
    for f in all_files:
        try:
            file_date_str = f.stem.split('_')[-1]
            file_date = datetime.strptime(file_date_str, '%Y%m%d')
            if start_date <= file_date <= end_date:
                week_files.append(f)
        except (ValueError, IndexError) as e:
            logging.warning(f"無法解析檔案日期: {f.name}, 錯誤: {e}")
            continue
    
    logging.info(f"週別 W{process_week} 符合的檔案數量: {len(week_files)}")
    
    if week_files:
        logging.info("前5個檔案:")
        for i, file_path in enumerate(week_files[:5]):
            logging.info(f"  {i+1}. {file_path.name}")
    
    return len(week_files) > 0

if __name__ == "__main__":
    print("開始測試週別處理程式...")
    
    print("\n=== 測試基本功能 ===")
    test_basic_functions()
    
    print("\n=== 測試檔案收集 ===")
    has_files = test_file_collection()
    
    print(f"\n測試完成！檔案收集結果: {'成功' if has_files else '無檔案'}")
    print("詳細日誌請查看 test_weekly_processor.log")
