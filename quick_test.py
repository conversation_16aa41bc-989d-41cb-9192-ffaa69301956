#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from datetime import datetime, timedelta
from pathlib import Path

print("=== 快速測試 ===")

# 測試週別計算
def get_week_number(date_str: str) -> int:
    date = datetime.strptime(date_str, '%Y%m%d')
    base_date = datetime.strptime('20250202', '%Y%m%d')  # W6 開始日期
    days_diff = (date - base_date).days
    week_diff = days_diff // 7
    return week_diff + 6  # 從 W6 開始

def get_current_week() -> int:
    today = datetime.now()
    today_str = today.strftime('%Y%m%d')
    return get_week_number(today_str)

# 測試基本功能
current_week = get_current_week()
process_week = current_week - 1

print(f"今日日期: {datetime.now().strftime('%Y-%m-%d')}")
print(f"當前週別: W{current_week}")
print(f"處理週別: W{process_week}")

# 測試路徑
BASE_LOG_PATH = r"\\k5dcnas02\IME\AAS_LOG"
BASE_OUTPUT_PATH = r"\\k5dcnas02\IME\個人資料夾\VICTOR\對對對對對\MO攔檢率\Rowdata"

base_log_path = Path(BASE_LOG_PATH)
base_output_path = Path(BASE_OUTPUT_PATH)

print(f"日誌路徑: {base_log_path}")
print(f"日誌路徑存在: {base_log_path.exists()}")
print(f"輸出路徑: {base_output_path}")
print(f"輸出路徑存在: {base_output_path.exists()}")

# 測試第一個資料夾
test_folder = 'DFLM_HKT-53001'
test_folder_path = base_log_path / test_folder
print(f"測試資料夾: {test_folder_path}")
print(f"測試資料夾存在: {test_folder_path.exists()}")

if test_folder_path.exists():
    subdirs = [d for d in test_folder_path.iterdir() if d.is_dir()]
    print(f"子資料夾數量: {len(subdirs)}")
    if subdirs:
        print("前3個子資料夾:")
        for subdir in sorted(subdirs)[:3]:
            print(f"  {subdir.name}")

print("=== 測試完成 ===")

# 寫入測試檔案
try:
    with open('test_output.txt', 'w', encoding='utf-8') as f:
        f.write(f"測試時間: {datetime.now()}\n")
        f.write(f"當前週別: W{current_week}\n")
        f.write(f"處理週別: W{process_week}\n")
        f.write(f"日誌路徑存在: {base_log_path.exists()}\n")
        f.write(f"輸出路徑存在: {base_output_path.exists()}\n")
    print("測試結果已寫入 test_output.txt")
except Exception as e:
    print(f"寫入檔案錯誤: {e}")
