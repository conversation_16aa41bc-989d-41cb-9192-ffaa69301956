#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化版 EXE 打包腳本
"""

import subprocess
import sys
import os

def install_packages():
    """安裝必要套件"""
    packages = ["pandas", "tqdm", "openpyxl", "pyinstaller"]
    
    for package in packages:
        print(f"安裝 {package}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ {package} 安裝成功")
        except:
            print(f"❌ {package} 安裝失敗")

def build_exe():
    """建立 EXE"""
    print("開始建立 EXE...")
    
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--console", 
        "--name=byweek2_processor",
        "--add-data=byweek2.py;.",
        "byweek2.py"
    ]
    
    try:
        subprocess.run(cmd, check=True)
        print("✅ EXE 建立成功！")
        print(f"檔案位置: {os.path.abspath('dist/byweek2_processor.exe')}")
    except:
        print("❌ EXE 建立失敗")

if __name__ == "__main__":
    print("開始打包 byweek2.py...")
    
    # 檢查檔案
    if not os.path.exists("byweek2.py"):
        print("❌ 找不到 byweek2.py")
        exit(1)
    
    # 安裝套件
    install_packages()
    
    # 建立 EXE
    build_exe()
    
    print("完成！")
    input("按 Enter 鍵退出...")
