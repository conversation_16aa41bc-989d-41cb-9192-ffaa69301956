#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
診斷 Excel 輸出問題
"""

from datetime import datetime, timedelta
from pathlib import Path

# 基礎路徑配置
BASE_LOG_PATH = r"\\k5dcnas02\IME\AAS_LOG"
BASE_OUTPUT_PATH = r"\\k5dcnas02\IME\個人資料夾\VICTOR\對對對對對\MO攔檢率\Rowdata"

def get_week_number(date_str: str) -> int:
    """根據日期取得週別"""
    date = datetime.strptime(date_str, '%Y%m%d')
    base_date = datetime.strptime('20250202', '%Y%m%d')  # W6 開始日期
    days_diff = (date - base_date).days
    week_diff = days_diff // 7
    return week_diff + 6  # 從 W6 開始

def get_date_range_for_week(week: int):
    """取得指定週別的日期範圍"""
    base_date = datetime.strptime('20250202', '%Y%m%d')  # W6 開始日期
    days_to_add = (week - 6) * 7  # 從 W6 開始計算
    start_date = base_date + timedelta(days=days_to_add)
    end_date = start_date + timedelta(days=6)
    return start_date, end_date

def get_current_week() -> int:
    """取得當前週別"""
    today = datetime.now()
    today_str = today.strftime('%Y%m%d')
    return get_week_number(today_str)

def diagnose_paths():
    """診斷路徑問題"""
    print("=== 路徑診斷 ===")
    
    # 檢查基礎路徑
    base_log_path = Path(BASE_LOG_PATH)
    base_output_path = Path(BASE_OUTPUT_PATH)
    
    print(f"日誌基礎路徑: {base_log_path}")
    print(f"日誌路徑存在: {base_log_path.exists()}")
    
    print(f"輸出基礎路徑: {base_output_path}")
    print(f"輸出路徑存在: {base_output_path.exists()}")
    
    # 檢查第一個資料夾
    test_folder = "DFLM_HKT-53001"
    test_log_folder = base_log_path / test_folder
    test_output_folder = base_output_path / test_folder
    
    print(f"\n測試資料夾: {test_folder}")
    print(f"日誌資料夾: {test_log_folder}")
    print(f"日誌資料夾存在: {test_log_folder.exists()}")
    print(f"輸出資料夾: {test_output_folder}")
    print(f"輸出資料夾存在: {test_output_folder.exists()}")
    
    return base_log_path.exists() and base_output_path.exists()

def diagnose_week_files():
    """診斷週別檔案"""
    print("\n=== 週別檔案診斷 ===")
    
    current_week = get_current_week()
    test_week = current_week - 1
    start_date, end_date = get_date_range_for_week(test_week)
    
    print(f"當前週別: W{current_week}")
    print(f"測試週別: W{test_week}")
    print(f"日期範圍: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
    
    # 檢查需要的月份
    months_to_check = set()
    current_date = start_date
    while current_date <= end_date:
        months_to_check.add(current_date.strftime('%Y%m'))
        current_date += timedelta(days=1)
    
    print(f"需要檢查的月份: {sorted(months_to_check)}")
    
    # 檢查第一個資料夾的檔案
    test_folder = "DFLM_HKT-53001"
    base_folder = Path(BASE_LOG_PATH) / test_folder
    
    if not base_folder.exists():
        print(f"❌ 資料夾不存在: {base_folder}")
        return False
    
    all_files = []
    for month in months_to_check:
        month_folder = base_folder / month
        print(f"\n檢查月份資料夾: {month_folder}")
        print(f"月份資料夾存在: {month_folder.exists()}")
        
        if month_folder.exists():
            month_files = list(month_folder.glob('*.log'))
            all_files.extend(month_files)
            print(f"找到 {len(month_files)} 個 .log 檔案")
            
            # 顯示前3個檔案
            for i, f in enumerate(month_files[:3]):
                print(f"  {i+1}. {f.name}")
    
    # 過濾週別檔案
    week_files = []
    for f in all_files:
        try:
            file_date_str = f.stem.split('_')[-1]
            file_date = datetime.strptime(file_date_str, '%Y%m%d')
            if start_date <= file_date <= end_date:
                week_files.append(f)
        except (ValueError, IndexError) as e:
            print(f"無法解析檔案日期: {f.name}, 錯誤: {e}")
    
    print(f"\n週別 W{test_week} 符合的檔案數量: {len(week_files)}")
    
    if week_files:
        print("符合週別的檔案:")
        for i, f in enumerate(week_files[:5]):
            print(f"  {i+1}. {f.name}")
    
    return len(week_files) > 0

def diagnose_output_path():
    """診斷輸出路徑"""
    print("\n=== 輸出路徑診斷 ===")
    
    current_week = get_current_week()
    test_week = current_week - 1
    start_date, end_date = get_date_range_for_week(test_week)
    
    test_folder = "DFLM_HKT-53001"
    output_folder = Path(BASE_OUTPUT_PATH) / test_folder
    
    print(f"輸出資料夾: {output_folder}")
    print(f"輸出資料夾存在: {output_folder.exists()}")
    
    # 嘗試創建輸出資料夾
    try:
        output_folder.mkdir(parents=True, exist_ok=True)
        print("✅ 輸出資料夾創建成功")
        
        # 檢查預期的 Excel 檔案名稱
        year_suffix = start_date.strftime('%y')
        expected_file = output_folder / f"{test_folder}_{year_suffix}_W{test_week:02d}.xlsx"
        print(f"預期 Excel 檔案: {expected_file}")
        print(f"Excel 檔案存在: {expected_file.exists()}")
        
        # 列出現有的檔案
        if output_folder.exists():
            existing_files = list(output_folder.glob('*.xlsx'))
            print(f"現有 Excel 檔案數量: {len(existing_files)}")
            for f in existing_files:
                print(f"  - {f.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 創建輸出資料夾失敗: {e}")
        return False

def check_existing_excel_files():
    """檢查現有的 Excel 檔案"""
    print("\n=== 現有 Excel 檔案檢查 ===")
    
    base_output_path = Path(BASE_OUTPUT_PATH)
    if not base_output_path.exists():
        print("❌ 輸出基礎路徑不存在")
        return
    
    # 檢查所有資料夾
    folders = [
        'DFLM_HKT-53001', 'DFLM_HKT-53002', 'DFLM_HKT-53003',
        'DV_V_AP-53201', 'DV_V_AP-53202', 'DV_V_AP-53301',
        'LA_NM-53001', 'LA_NM-53002', 'LA_NM-53003', 'LA_NM-53004',
        'MEC_FK-53101', 'MEC_FK-53301', 'SMVC_RH600-53002'
    ]
    
    total_excel_files = 0
    
    for folder in folders:
        folder_path = base_output_path / folder
        if folder_path.exists():
            excel_files = list(folder_path.glob('*.xlsx'))
            if excel_files:
                print(f"\n{folder}: {len(excel_files)} 個 Excel 檔案")
                for f in excel_files[-3:]:  # 顯示最新的3個檔案
                    file_size = f.stat().st_size
                    mod_time = datetime.fromtimestamp(f.stat().st_mtime)
                    print(f"  - {f.name} ({file_size} bytes, {mod_time.strftime('%Y-%m-%d %H:%M')})")
                total_excel_files += len(excel_files)
    
    print(f"\n總共找到 {total_excel_files} 個 Excel 檔案")

def main():
    """主診斷程式"""
    print("開始診斷 Excel 輸出問題...")
    
    # 1. 路徑診斷
    paths_ok = diagnose_paths()
    
    # 2. 週別檔案診斷
    files_ok = diagnose_week_files()
    
    # 3. 輸出路徑診斷
    output_ok = diagnose_output_path()
    
    # 4. 檢查現有檔案
    check_existing_excel_files()
    
    # 總結
    print("\n=== 診斷總結 ===")
    print(f"路徑檢查: {'✅ 通過' if paths_ok else '❌ 失敗'}")
    print(f"檔案檢查: {'✅ 通過' if files_ok else '❌ 失敗'}")
    print(f"輸出檢查: {'✅ 通過' if output_ok else '❌ 失敗'}")
    
    if paths_ok and files_ok and output_ok:
        print("\n✅ 所有檢查都通過，Excel 輸出功能應該正常工作")
    else:
        print("\n❌ 發現問題，需要修正")

if __name__ == "__main__":
    main()
