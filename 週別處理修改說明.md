# 週別資料處理修改說明

## 主要修改內容

### 1. 檔案路徑修改
**原本（月份處理）：**
```python
month_folder = folder.parent / folder.name / month
```

**修改後（週別處理）：**
```python
month_folder = folder / month  # 直接在folder下找月份資料夾
```

### 2. 檔案過濾邏輯改進
**原本：**
```python
files = [f for f in all_files if start_date <= datetime.strptime(f.stem.split('_')[-1], '%Y%m%d') <= end_date]
```

**修改後：**
```python
files = []
for f in all_files:
    try:
        # 從檔案名稱中提取日期
        file_date_str = f.stem.split('_')[-1]
        file_date = datetime.strptime(file_date_str, '%Y%m%d')
        if start_date <= file_date <= end_date:
            files.append(f)
    except (ValueError, IndexError) as e:
        logging.warning(f"無法解析檔案日期: {f.name}, 錯誤: {e}")
        continue
```

### 3. 新增的功能函數

#### `process_week_data(week, folders=None)`
- 處理指定週別的資料
- 支援指定特定資料夾列表
- 自動跨月份收集檔案

#### `process_multiple_weeks(start_week, end_week, folders=None)`
- 批量處理多個週別
- 支援週別範圍處理

#### 改進的 `main()` 函數
- 自動計算當前週別
- 預設處理上一週的資料

## 主要改進點

### 1. 跨月份處理能力
- **問題**：一週可能跨越兩個月份（例如：月底到月初）
- **解決**：自動識別週別涉及的所有月份，收集相關檔案

### 2. 更好的錯誤處理
- **問題**：檔案名稱解析失敗會導致程式中斷
- **解決**：使用 try-catch 處理檔案名稱解析錯誤，記錄警告但繼續處理

### 3. 靈活的處理方式
- **單一週別處理**：`process_week_data(25)`
- **多週別批量處理**：`process_multiple_weeks(17, 25)`
- **特定資料夾處理**：`process_week_data(25, ['DFLM_HKT-53001'])`

### 4. 詳細的日誌記錄
- 顯示週別對應的日期範圍
- 記錄每個月份找到的檔案數量
- 顯示最終符合條件的檔案列表

## 使用範例

### 基本使用
```python
# 處理第25週的所有資料夾
process_week_data(25)

# 處理第17週到第25週
process_multiple_weeks(17, 25)

# 只處理特定資料夾的第25週
process_week_data(25, ['DFLM_HKT-53001', 'DFLM_HKT-53002'])
```

### 查看週別對應日期
```python
week = 25
start_date, end_date = get_date_range_for_week(week)
print(f"週別 W{week}: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
```

## 檔案結構假設

程式假設檔案結構如下：
```
\\k5dcnas02\IME\AAS_LOG\
├── DFLM_HKT-53001\
│   ├── 202505\
│   │   ├── file_20250501.log
│   │   ├── file_20250502.log
│   │   └── ...
│   ├── 202506\
│   │   ├── file_20250601.log
│   │   └── ...
│   └── ...
├── DFLM_HKT-53002\
└── ...
```

## 輸出檔案命名

輸出檔案命名格式：`{資料夾名稱}_{年份後兩位}_W{週別}.xlsx`

例如：`DFLM_HKT-53001_25_W25.xlsx`

## 注意事項

1. **週別計算基準**：以 2025/02/02 為 W6 開始日期
2. **檔案名稱格式**：假設檔案名稱以 `_YYYYMMDD.log` 結尾
3. **跨月份處理**：自動處理跨月份的週別資料
4. **錯誤容忍**：檔案名稱解析錯誤不會中斷整個處理流程
